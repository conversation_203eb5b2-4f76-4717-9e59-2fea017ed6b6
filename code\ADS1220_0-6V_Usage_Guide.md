# ADS1220 0-6V输入电压处理使用指南

## 📋 系统概述

本系统将0-6V的外部输入电压通过硬件分压电路处理为0-3V，然后通过ADS1220的AIN0通道进行高精度采集，最终通过SPI传输到主控板。

## ⚙️ 硬件配置

### 电压处理链路
```
外部输入(0-6V) → 分压电路(2:1) → ADS1220 AIN0(0-3V) → SPI → 主控板
```

### ADS1220配置参数
- **基准电压**: 3.3V (AVDD/AVSS)
- **输入模式**: 单端输入 (AIN0相对于AVSS)
- **增益**: 1倍
- **采样率**: 20 SPS
- **转换模式**: 连续转换
- **分辨率**: 24位

### 硬件连接
- **SPI接口**: PB3(SCK), PB4(MISO), PB5(MOSI)
- **片选CS**: PA6
- **数据就绪DRDY**: PA7 (需根据实际连接修改)

## 🔧 软件配置

### 初始化
系统启动时自动调用 `ADS1220_Init()` 函数，配置为3.3V基准的单端输入模式。

### 电压转换公式
```c
// ADC测量电压 (0-3V)
adc_voltage = (float)raw_data * 3.3f / 8388608.0f;

// 实际输入电压 (0-6V)
actual_voltage = adc_voltage * 2.0f;
```

## 📡 RS485测试命令

### 1. 全通道测试
```bash
command:ads1220_test
```
**输出示例**:
```
=== ADS1220 0-6V输入测试 ===
基准电压: 3.3V, 输入范围: 0-6V -> 0-3V
AIN0: 实际输入=2.456V, ADC测量=1.228V, 原始数据=3145728
AIN1: 实际输入=0.000V, ADC测量=0.000V, 原始数据=0
AIN2: 实际输入=0.000V, ADC测量=0.000V, 原始数据=0
AIN3: 实际输入=0.000V, ADC测量=0.000V, 原始数据=0
=== 测试完成 ===
```

### 2. 单通道详细测试
```bash
command:ads1220_ch = 0
```
**输出示例**:
```
=== AIN0 详细信息 ===
原始输入电压: 2.456789V (0-6V范围)
ADC测量电压: 1.228394V (0-3V范围)
ADC原始数据: 3145728 (24位)
电压转换比: 2.0:1
```

### 3. AIN0快速测试
```bash
command:ads1220_ain0
```
**输出示例**:
```
AIN0实时电压: 2.456789V
```

## 💻 编程接口

### 主要函数

#### 1. 获取实际输入电压 (0-6V)
```c
float voltage = ADS1220_GetAIN0_Voltage();
// 返回: 0-6V范围的实际输入电压
```

#### 2. 获取详细电压信息
```c
float adc_voltage, actual_voltage;
int32_t raw_data;

ADS1220_GetVoltageDetails(0, &adc_voltage, &actual_voltage, &raw_data);
// adc_voltage: ADC测量的电压 (0-3V)
// actual_voltage: 实际输入电压 (0-6V)
// raw_data: 24位原始ADC数据
```

#### 3. 电压监控处理
```c
void ADS1220_ProcessVoltage(void);
// 每100ms自动处理电压数据，包含报警检查
```

#### 4. SPI通信状态检查
```c
uint8_t status = ADS1220_CheckSPIStatus();
// 返回: 1=通信正常, 0=通信异常
```

### 使用示例

#### 基本电压读取
```c
void voltage_monitoring_task(void)
{
    // 获取实时电压
    float input_voltage = ADS1220_GetAIN0_Voltage();
    
    // 电压范围检查
    if (input_voltage > 5.5f) {
        // 过压保护
        rs485_printf("过压报警: %.3fV\r\n", input_voltage);
    } else if (input_voltage < 0.1f) {
        // 欠压或断线检测
        rs485_printf("欠压报警: %.3fV\r\n", input_voltage);
    }
    
    // 数据记录或其他处理
    // ...
}
```

#### 高精度测量
```c
void precision_measurement(void)
{
    float adc_voltage, actual_voltage;
    int32_t raw_data;
    
    // 获取详细测量数据
    ADS1220_GetVoltageDetails(0, &adc_voltage, &actual_voltage, &raw_data);
    
    // 精度分析
    float resolution = 6.0f / 8388608.0f; // 约0.715μV
    
    rs485_printf("测量精度: %.3fμV\r\n", resolution * 1000000);
    rs485_printf("当前电压: %.6fV\r\n", actual_voltage);
}
```

## 🔍 故障排除

### 常见问题

1. **电压读数为0**
   - 检查硬件连接
   - 确认分压电路工作正常
   - 验证SPI通信: `ADS1220_CheckSPIStatus()`

2. **电压读数不准确**
   - 检查3.3V基准电压是否稳定
   - 确认分压比是否为2:1
   - 校准ADC基准电压

3. **SPI通信异常**
   - 检查SPI时钟和数据线连接
   - 确认CS片选信号正常
   - 验证SPI时序配置

### 调试命令
```bash
# 检查配置寄存器
command:ads1220_test

# 检查单通道详细信息
command:ads1220_ch = 0

# 实时监控
command:ads1220_ain0
```

## 📊 性能指标

- **输入范围**: 0-6V
- **测量精度**: 24位 (约0.715μV分辨率)
- **采样率**: 20 SPS
- **基准精度**: 取决于3.3V电源稳定性
- **非线性度**: <0.01% (典型值)
- **温度系数**: <10ppm/°C

## 🔧 系统集成

在主循环或定时任务中调用电压处理函数:
```c
void main_loop(void)
{
    while(1) {
        // 电压监控处理
        ADS1220_ProcessVoltage();
        
        // 其他系统任务
        // ...
        
        HAL_Delay(10);
    }
}
```
