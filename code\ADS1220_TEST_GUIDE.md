# ADS1220扩展板数据获取测试指南

## 📋 测试概述

本指南提供了完整的ADS1220扩展板数据获取测试方案，帮助您验证硬件连接和数据读取功能是否正常工作。

## 🚀 测试命令列表

### 1. 快速测试
```bash
command:quick_test
```
**功能**: 快速验证ADS1220是否能正常读取数据
**预期输出**:
```
=== 快速数据获取测试 ===
✓ 数据获取成功
AIN0电压: 2.456V
```

### 2. 硬件连接测试
```bash
command:test_hardware
```
**功能**: 全面测试ADS1220硬件连接状态
**预期输出**:
```
=== ADS1220硬件连接测试 ===
1. 测试SPI通信...
✓ SPI通信正常
  寄存器值: 0x60 0x00 0x80 0x00

2. 测试复位功能...
✓ 复位命令发送成功
✓ 复位后寄存器读取成功

3. 测试转换功能...
✓ 启动转换成功
✓ 数据读取成功
  原始数据: 3145728 (0x00300000)
✓ 数据在合理范围内

4. 测试用户ADC读取函数...
✓ 用户函数读取成功
  返回值: 1.228000
  原始数据: 3145728

=== 硬件连接测试完成 ===
```

### 3. 用户ADC函数测试
```bash
command:test_user_adc
```
**功能**: 测试您自定义的ADC读取函数
**预期输出**:
```
=== ADC读取测试 ===
✓ ADC读取成功
原始数据: 1.228000
实际电压: 3145728
=== 测试完成 ===
```

### 4. 完整扩展板测试
```bash
command:test_ads1220_board
```
**功能**: 最全面的扩展板功能测试
**预期输出**:
```
=== ADS1220扩展板数据获取测试 ===
1. 检查SPI通信...
✓ SPI通信正常
  寄存器值: 0x60 0x00 0x80 0x00

2. 测试原始数据读取...
✓ 原始数据读取成功
  原始数据: 3145728 (0x00300000)

3. 测试AIN0电压读取...
✓ AIN0电压读取成功
  外部电压: 2.456000V
✓ 电压值在合理范围内

4. 连续采样测试(5次)...
  采样1: 2.456V
  采样2: 2.457V
  采样3: 2.455V
  采样4: 2.456V
  采样5: 2.456V
✓ 连续采样完成
  平均值: 2.456000V
  最大偏差: 0.002000V
✓ 采样稳定性良好

=== 扩展板测试完成 ===
```

## 🔍 故障诊断

### 常见错误及解决方案

#### 1. SPI通信失败
**错误信息**: `✗ SPI通信失败 - 检查SPI连线`
**可能原因**:
- SPI线路连接错误
- CS引脚配置错误
- 时钟频率过高

**解决方案**:
- 检查SPI3连线：PB3(SCK), PB4(MISO), PB5(MOSI)
- 确认CS引脚：PA6
- 验证供电电压3.3V

#### 2. 读取全为0xFF
**错误信息**: `✗ 读取全为0xFF - 可能MISO线未连接`
**可能原因**:
- MISO线断开或接触不良
- ADS1220芯片损坏

**解决方案**:
- 检查MISO线连接
- 用万用表测试连续性
- 更换ADS1220芯片

#### 3. 读取全为0x00
**错误信息**: `⚠ 读取全为0x00 - 可能芯片未上电或复位`
**可能原因**:
- 供电不足
- 复位信号异常
- 芯片未正确初始化

**解决方案**:
- 检查3.3V供电
- 确认复位电路
- 重新初始化芯片

#### 4. 数据读取失败
**错误信息**: `✗ 数据读取失败`
**可能原因**:
- DRDY信号异常
- 转换超时
- SPI时序问题

**解决方案**:
- 检查DRDY引脚连接
- 增加转换等待时间
- 调整SPI时序参数

#### 5. 电压值异常
**错误信息**: `⚠ 电压值超出预期范围(0-6V)`
**可能原因**:
- 输入信号超出范围
- 降压电路故障
- 基准电压不准确

**解决方案**:
- 检查输入信号范围
- 验证降压电路2:1比例
- 校准基准电压

## 📊 测试结果判断

### 正常工作指标
- ✅ SPI通信正常
- ✅ 寄存器读写正确
- ✅ 数据转换成功
- ✅ 电压值在0-6V范围内
- ✅ 连续采样稳定性好（偏差<0.01V）

### 异常情况处理
- ❌ 任何步骤失败 → 检查硬件连接
- ⚠️ 数据异常 → 检查输入信号和电路
- 🔄 间歇性故障 → 检查供电稳定性

## 🛠️ 调试建议

### 1. 逐步测试
按以下顺序进行测试：
1. `command:quick_test` - 基本功能
2. `command:test_hardware` - 硬件连接
3. `command:test_user_adc` - 用户函数
4. `command:test_ads1220_board` - 完整测试

### 2. 硬件检查清单
- [ ] 供电电压3.3V稳定
- [ ] SPI连线正确无误
- [ ] CS引脚配置正确
- [ ] DRDY引脚连接（如果使用）
- [ ] 降压电路工作正常
- [ ] 输入信号在0-6V范围内

### 3. 软件检查清单
- [ ] ADS1220驱动正确初始化
- [ ] SPI配置参数正确
- [ ] 时序参数合适
- [ ] 中断配置正确（如果使用）

## 📈 性能验证

### 精度测试
使用已知电压源（如万用表校准源）输入，对比测量结果：
```bash
command:voltage_detail  # 查看详细测量信息
```

### 稳定性测试
连续测量同一信号，观察数值变化：
```bash
command:test_ads1220_board  # 包含5次连续采样
```

### 响应时间测试
改变输入信号，观察响应速度：
```bash
command:quick_test  # 多次执行观察响应
```

## 🎯 测试成功标准

当所有测试命令都能正常执行并返回合理数据时，说明ADS1220扩展板数据获取功能正常。具体标准：

1. **SPI通信**: 能正确读写寄存器
2. **数据转换**: 能获取24位ADC数据
3. **电压测量**: 0-6V范围内测量准确
4. **稳定性**: 连续测量偏差<1%
5. **响应性**: 转换时间<100ms

满足以上条件即可确认扩展板工作正常，可以进行实际应用开发。
