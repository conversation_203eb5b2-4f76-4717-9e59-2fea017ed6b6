/**
 * @file ADS1220.c
 * @brief ADS1220 24位ADC驱动程序 - 单端输入模式
 * <AUTHOR> Assistant
 * @date 2025
 */

#include "ADS1220.h"

// 全局变量
static SPI_HandleTypeDef *ads1220_spi = NULL;
static ADS1220_Config_t ads1220_config;

/**
 * @brief 初始化ADS1220
 * @param hspi SPI句柄指针
 * @retval HAL_StatusTypeDef 初始化状态
 */
HAL_StatusTypeDef ADS1220_Init(SPI_HandleTypeDef *hspi)
{
    ads1220_spi = hspi;

    // 确保CS为高电平
    ADS1220_CS_HIGH();
    HAL_Delay(10);

    // 复位ADS1220
    if (ADS1220_Reset() != HAL_OK) {
        return HAL_ERROR;
    }

    // 配置为单端输入模式
    ads1220_config.mux = ADS1220_MUX_AIN0_AVSS;      // AIN0单端输入
    ads1220_config.gain = ADS1220_GAIN_1;            // 增益1倍
    ads1220_config.pga_bypass = ADS1220_PGA_ENABLE;  // 启用PGA
    ads1220_config.data_rate = ADS1220_DR_20SPS;     // 20SPS采样率
    ads1220_config.op_mode = ADS1220_MODE_NORMAL;    // 正常模式
    ads1220_config.conv_mode = ADS1220_CM_SINGLE;    // 单次转换
    ads1220_config.vref = ADS1220_VREF_AVDD_AVSS;    // 使用AVDD/AVSS作为基准

    // 写入配置
    if (ADS1220_WriteConfig(&ads1220_config) != HAL_OK) {
        return HAL_ERROR;
    }

    return HAL_OK;
}

/**
 * @brief 复位ADS1220
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_Reset(void)
{
    uint8_t cmd = ADS1220_CMD_RESET;

    ADS1220_CS_LOW();
    HAL_Delay(1);

    HAL_StatusTypeDef status = HAL_SPI_Transmit(ads1220_spi, &cmd, 1, ADS1220_TIMEOUT);

    HAL_Delay(1);
    ADS1220_CS_HIGH();
    HAL_Delay(50); // 复位后等待时间

    return status;
}

/**
 * @brief 写入配置到ADS1220
 * @param config 配置结构体指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_WriteConfig(ADS1220_Config_t *config)
{
    uint8_t reg_data[4];

    // 配置寄存器0
    reg_data[0] = (config->mux << 4) | (config->gain << 1) | config->pga_bypass;

    // 配置寄存器1
    reg_data[1] = (config->data_rate << 5) | (config->op_mode << 3) |
                  (config->conv_mode << 2) | (config->temp_sensor << 1) | config->burn_out;

    // 配置寄存器2
    reg_data[2] = (config->vref << 6) | (config->fir_filter << 4) |
                  (config->low_side_switch << 3) | config->idac_current;

    // 配置寄存器3
    reg_data[3] = (config->idac1_routing << 5) | (config->idac2_routing << 2) |
                  (config->drdy_mode << 1) | config->reserved;

    // 写入所有配置寄存器
    return ADS1220_WriteRegister(ADS1220_REG_CONFIG0, 4, reg_data);
}

/**
 * @brief 写入寄存器
 * @param reg_addr 寄存器地址
 * @param num_regs 寄存器数量
 * @param data 数据指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_WriteRegister(uint8_t reg_addr, uint8_t num_regs, uint8_t *data)
{
    uint8_t cmd = ADS1220_CMD_WREG | ((reg_addr << 2) & 0x0C) | ((num_regs - 1) & 0x03);

    ADS1220_CS_LOW();
    HAL_Delay(1);

    // 发送命令
    HAL_StatusTypeDef status = HAL_SPI_Transmit(ads1220_spi, &cmd, 1, ADS1220_TIMEOUT);
    if (status != HAL_OK) {
        ADS1220_CS_HIGH();
        return status;
    }

    // 发送数据
    status = HAL_SPI_Transmit(ads1220_spi, data, num_regs, ADS1220_TIMEOUT);

    HAL_Delay(1);
    ADS1220_CS_HIGH();
    HAL_Delay(5); // 寄存器写入处理时间

    return status;
}

/**
 * @brief 读取寄存器
 * @param reg_addr 寄存器地址
 * @param num_regs 寄存器数量
 * @param data 数据指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_ReadRegister(uint8_t reg_addr, uint8_t num_regs, uint8_t *data)
{
    uint8_t cmd = ADS1220_CMD_RREG | ((reg_addr << 2) & 0x0C) | ((num_regs - 1) & 0x03);

    ADS1220_CS_LOW();
    HAL_Delay(1);

    // 发送命令
    HAL_StatusTypeDef status = HAL_SPI_Transmit(ads1220_spi, &cmd, 1, ADS1220_TIMEOUT);
    if (status != HAL_OK) {
        ADS1220_CS_HIGH();
        return status;
    }

    // 读取数据
    status = HAL_SPI_Receive(ads1220_spi, data, num_regs, ADS1220_TIMEOUT);

    HAL_Delay(1);
    ADS1220_CS_HIGH();

    return status;
}

/**
 * @brief 设置单端输入通道
 * @param channel 通道选择 (ADS1220_CHANNEL_AIN0/1/2/3)
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_SetSingleEndedChannel(ADS1220_Channel_t channel)
{
    uint8_t mux_setting;

    switch(channel) {
        case ADS1220_CHANNEL_AIN0:
            mux_setting = ADS1220_MUX_AIN0_AVSS;
            break;
        case ADS1220_CHANNEL_AIN1:
            mux_setting = ADS1220_MUX_AIN1_AVSS;
            break;
        case ADS1220_CHANNEL_AIN2:
            mux_setting = ADS1220_MUX_AIN2_AVSS;
            break;
        case ADS1220_CHANNEL_AIN3:
            mux_setting = ADS1220_MUX_AIN3_AVSS;
            break;
        default:
            return HAL_ERROR;
    }

    // 读取当前配置寄存器0
    uint8_t reg_data;
    if (ADS1220_ReadRegister(ADS1220_REG_CONFIG0, 1, &reg_data) != HAL_OK) {
        return HAL_ERROR;
    }

    // 更新MUX设置
    reg_data = (reg_data & 0x0F) | (mux_setting << 4);

    // 写回寄存器
    return ADS1220_WriteRegister(ADS1220_REG_CONFIG0, 1, &reg_data);
}

/**
 * @brief 启动转换
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_StartConversion(void)
{
    uint8_t cmd = ADS1220_CMD_START;

    ADS1220_CS_LOW();
    HAL_Delay(1);

    HAL_StatusTypeDef status = HAL_SPI_Transmit(ads1220_spi, &cmd, 1, ADS1220_TIMEOUT);

    HAL_Delay(1);
    ADS1220_CS_HIGH();

    return status;
}

/**
 * @brief 读取ADC数据
 * @param data 数据指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_ReadData(int32_t *data)
{
    uint8_t cmd = ADS1220_CMD_RDATA;
    uint8_t rx_data[3];

    ADS1220_CS_LOW();
    HAL_Delay(1);

    // 发送读数据命令
    HAL_StatusTypeDef status = HAL_SPI_Transmit(ads1220_spi, &cmd, 1, ADS1220_TIMEOUT);
    if (status != HAL_OK) {
        ADS1220_CS_HIGH();
        return status;
    }

    // 读取24位数据
    status = HAL_SPI_Receive(ads1220_spi, rx_data, 3, ADS1220_TIMEOUT);

    HAL_Delay(1);
    ADS1220_CS_HIGH();

    if (status == HAL_OK) {
        // 组合24位数据并进行符号扩展
        *data = ((int32_t)rx_data[0] << 16) | ((int32_t)rx_data[1] << 8) | rx_data[2];

        // 符号扩展到32位
        if (*data & 0x800000) {
            *data |= 0xFF000000;
        }
    }

    return status;
}

/**
 * @brief 等待数据就绪
 * @param timeout_ms 超时时间(毫秒)
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_WaitDataReady(uint32_t timeout_ms)
{
    uint32_t start_time = HAL_GetTick();

    // 等待DRDY引脚变为低电平
    while (HAL_GPIO_ReadPin(ADS1220_DRDY_PORT, ADS1220_DRDY_PIN) == GPIO_PIN_SET) {
        if ((HAL_GetTick() - start_time) > timeout_ms) {
            return HAL_TIMEOUT;
        }
        HAL_Delay(1);
    }

    return HAL_OK;
}

/**
 * @brief 读取单端输入电压
 * @param channel 通道选择
 * @param voltage 电压值指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_ReadSingleEndedVoltage(ADS1220_Channel_t channel, float *voltage)
{
    int32_t adc_data;

    // 设置通道
    if (ADS1220_SetSingleEndedChannel(channel) != HAL_OK) {
        return HAL_ERROR;
    }

    // 启动转换
    if (ADS1220_StartConversion() != HAL_OK) {
        return HAL_ERROR;
    }

    // 等待转换完成
    if (ADS1220_WaitDataReady(ADS1220_CONVERSION_TIMEOUT) != HAL_OK) {
        return HAL_TIMEOUT;
    }

    // 读取数据
    if (ADS1220_ReadData(&adc_data) != HAL_OK) {
        return HAL_ERROR;
    }

    // 转换为电压值 (假设AVDD=3.3V, 增益=1)
    *voltage = ((float)adc_data * ADS1220_VREF_VOLTAGE) / ADS1220_FULL_SCALE;

    return HAL_OK;
}

/**
 * @brief 验证ADS1220配置是否正确
 * @param expected_config 期望的配置结构体指针
 * @retval HAL_StatusTypeDef 验证结果
 */
HAL_StatusTypeDef ADS1220_VerifyConfig(ADS1220_Config_t *expected_config)
{
    uint8_t reg_data[4];
    uint8_t expected_reg[4];

    // 读取当前配置
    if (ADS1220_ReadRegister(ADS1220_REG_CONFIG0, 4, reg_data) != HAL_OK) {
        return HAL_ERROR;
    }

    // 计算期望的寄存器值
    expected_reg[0] = (expected_config->mux << 4) | (expected_config->gain << 1) | expected_config->pga_bypass;
    expected_reg[1] = (expected_config->data_rate << 5) | (expected_config->op_mode << 3) |
                      (expected_config->conv_mode << 2) | (expected_config->temp_sensor << 1) | expected_config->burn_out;
    expected_reg[2] = (expected_config->vref << 6) | (expected_config->fir_filter << 4) |
                      (expected_config->low_side_switch << 3) | expected_config->idac_current;
    expected_reg[3] = (expected_config->idac1_routing << 5) | (expected_config->idac2_routing << 2) |
                      (expected_config->drdy_mode << 1) | expected_config->reserved;

    // 比较配置
    for (int i = 0; i < 4; i++) {
        if (reg_data[i] != expected_reg[i]) {
            return HAL_ERROR; // 配置不匹配
        }
    }

    return HAL_OK; // 配置匹配
}

/**
 * @brief 获取当前ADS1220配置
 * @param current_config 当前配置结构体指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_GetCurrentConfig(ADS1220_Config_t *current_config)
{
    uint8_t reg_data[4];

    // 读取所有配置寄存器
    if (ADS1220_ReadRegister(ADS1220_REG_CONFIG0, 4, reg_data) != HAL_OK) {
        return HAL_ERROR;
    }

    // 解析寄存器0
    current_config->mux = (reg_data[0] & 0xF0) >> 4;
    current_config->gain = (reg_data[0] & 0x0E) >> 1;
    current_config->pga_bypass = reg_data[0] & 0x01;

    // 解析寄存器1
    current_config->data_rate = (reg_data[1] & 0xE0) >> 5;
    current_config->op_mode = (reg_data[1] & 0x18) >> 3;
    current_config->conv_mode = (reg_data[1] & 0x04) >> 2;
    current_config->temp_sensor = (reg_data[1] & 0x02) >> 1;
    current_config->burn_out = reg_data[1] & 0x01;

    // 解析寄存器2
    current_config->vref = (reg_data[2] & 0xC0) >> 6;
    current_config->fir_filter = (reg_data[2] & 0x30) >> 4;
    current_config->low_side_switch = (reg_data[2] & 0x08) >> 3;
    current_config->idac_current = reg_data[2] & 0x07;

    // 解析寄存器3
    current_config->idac1_routing = (reg_data[3] & 0xE0) >> 5;
    current_config->idac2_routing = (reg_data[3] & 0x1C) >> 2;
    current_config->drdy_mode = (reg_data[3] & 0x02) >> 1;
    current_config->reserved = reg_data[3] & 0x01;

    return HAL_OK;
}

/**
 * @brief 打印ADS1220配置信息（调试用）
 * @param config 配置结构体指针
 * @param title 标题字符串
 */
void ADS1220_PrintConfig(ADS1220_Config_t *config, const char *title)
{
    // 注意：这个函数需要printf支持，在实际使用中可能需要替换为rs485_printf
    printf("=== %s ===\n", title);
    printf("MUX: 0x%X ", config->mux);
    switch(config->mux) {
        case ADS1220_MUX_AIN0_AVSS: printf("(AIN0单端)\n"); break;
        case ADS1220_MUX_AIN1_AVSS: printf("(AIN1单端)\n"); break;
        case ADS1220_MUX_AIN2_AVSS: printf("(AIN2单端)\n"); break;
        case ADS1220_MUX_AIN3_AVSS: printf("(AIN3单端)\n"); break;
        default: printf("(其他模式)\n"); break;
    }
    printf("增益: %d倍\n", 1 << config->gain);
    printf("PGA: %s\n", config->pga_bypass ? "旁路" : "启用");
    printf("数据速率: ");
    switch(config->data_rate) {
        case ADS1220_DR_20SPS: printf("20 SPS\n"); break;
        case ADS1220_DR_45SPS: printf("45 SPS\n"); break;
        case ADS1220_DR_90SPS: printf("90 SPS\n"); break;
        case ADS1220_DR_175SPS: printf("175 SPS\n"); break;
        case ADS1220_DR_330SPS: printf("330 SPS\n"); break;
        case ADS1220_DR_600SPS: printf("600 SPS\n"); break;
        case ADS1220_DR_1000SPS: printf("1000 SPS\n"); break;
        default: printf("未知\n"); break;
    }
    printf("工作模式: %s\n", config->op_mode == ADS1220_MODE_NORMAL ? "正常" : "其他");
    printf("转换模式: %s\n", config->conv_mode == ADS1220_CM_SINGLE ? "单次" : "连续");
    printf("电压基准: ");
    switch(config->vref) {
        case ADS1220_VREF_INTERNAL: printf("内部2.048V\n"); break;
        case ADS1220_VREF_REFP_REFN: printf("外部REFP/REFN\n"); break;
        case ADS1220_VREF_AVDD_AVSS: printf("外部AVDD/AVSS\n"); break;
        case ADS1220_VREF_SUPPLY: printf("供电电压\n"); break;
    }
}