/**
 * @file ADS1220.h
 * @brief ADS1220 24位ADC驱动程序头文件 - 单端输入模式
 * <AUTHOR> Assistant
 * @date 2025
 */

#ifndef __ADS1220_H
#define __ADS1220_H

#ifdef __cplusplus
extern "C" {
#endif

/* 包含文件 */
#include "main.h"
#include "spi.h"
#include <stdint.h>
#include <stdbool.h>

/* 硬件配置 - 根据实际硬件连接修改 */
#define ADS1220_CS_PORT         GPIOB
#define ADS1220_CS_PIN          GPIO_PIN_6
#define ADS1220_DRDY_PORT       GPIOB
#define ADS1220_DRDY_PIN        GPIO_PIN_6

/* GPIO控制宏 */
#define ADS1220_CS_LOW()        HAL_GPIO_WritePin(ADS1220_CS_PORT, ADS1220_CS_PIN, GPIO_PIN_RESET)
#define ADS1220_CS_HIGH()       HAL_GPIO_WritePin(ADS1220_CS_PORT, ADS1220_CS_PIN, GPIO_PIN_SET)

/* 超时定义 */
#define ADS1220_TIMEOUT             1000    // SPI超时时间(ms)
#define ADS1220_CONVERSION_TIMEOUT  100     // 转换超时时间(ms)

/* 电压参考定义 */
#define ADS1220_VREF_VOLTAGE        3.3f    // 基准电压(V)
#define ADS1220_FULL_SCALE          8388608 // 2^23 (24位ADC)

/* ADS1220命令定义 */
#define ADS1220_CMD_RESET           0x06    // 复位命令
#define ADS1220_CMD_START           0x08    // 启动/同步命令
#define ADS1220_CMD_POWERDOWN       0x02    // 掉电命令
#define ADS1220_CMD_RDATA           0x10    // 读数据命令
#define ADS1220_CMD_RREG            0x20    // 读寄存器命令
#define ADS1220_CMD_WREG            0x40    // 写寄存器命令

/* 寄存器地址定义 */
#define ADS1220_REG_CONFIG0         0x00    // 配置寄存器0
#define ADS1220_REG_CONFIG1         0x01    // 配置寄存器1
#define ADS1220_REG_CONFIG2         0x02    // 配置寄存器2
#define ADS1220_REG_CONFIG3         0x03    // 配置寄存器3

/* 输入多路复用器配置 (CONFIG0[7:4]) */
#define ADS1220_MUX_AIN0_AIN1       0x0     // AIN0-AIN1差分
#define ADS1220_MUX_AIN0_AIN2       0x1     // AIN0-AIN2差分
#define ADS1220_MUX_AIN0_AIN3       0x2     // AIN0-AIN3差分
#define ADS1220_MUX_AIN1_AIN2       0x3     // AIN1-AIN2差分
#define ADS1220_MUX_AIN1_AIN3       0x4     // AIN1-AIN3差分
#define ADS1220_MUX_AIN2_AIN3       0x5     // AIN2-AIN3差分
#define ADS1220_MUX_AIN0_AVSS       0x6     // AIN0单端(相对AVSS)
#define ADS1220_MUX_AIN1_AVSS       0x7     // AIN1单端(相对AVSS)
#define ADS1220_MUX_AIN2_AVSS       0x8     // AIN2单端(相对AVSS)
#define ADS1220_MUX_AIN3_AVSS       0x9     // AIN3单端(相对AVSS)
#define ADS1220_MUX_REFP_REFN       0xA     // (REFP-REFN)/4
#define ADS1220_MUX_AVDD_AVSS       0xB     // (AVDD-AVSS)/4
#define ADS1220_MUX_SHORTED         0xC     // 短路输入

/* 增益配置 (CONFIG0[3:1]) */
#define ADS1220_GAIN_1              0x0     // 增益1倍
#define ADS1220_GAIN_2              0x1     // 增益2倍
#define ADS1220_GAIN_4              0x2     // 增益4倍
#define ADS1220_GAIN_8              0x3     // 增益8倍
#define ADS1220_GAIN_16             0x4     // 增益16倍
#define ADS1220_GAIN_32             0x5     // 增益32倍
#define ADS1220_GAIN_64             0x6     // 增益64倍
#define ADS1220_GAIN_128            0x7     // 增益128倍

/* PGA旁路配置 (CONFIG0[0]) */
#define ADS1220_PGA_ENABLE          0x0     // 启用PGA
#define ADS1220_PGA_BYPASS          0x1     // 旁路PGA

/* 数据速率配置 (CONFIG1[7:5]) */
#define ADS1220_DR_20SPS            0x0     // 20 SPS
#define ADS1220_DR_45SPS            0x1     // 45 SPS
#define ADS1220_DR_90SPS            0x2     // 90 SPS
#define ADS1220_DR_175SPS           0x3     // 175 SPS
#define ADS1220_DR_330SPS           0x4     // 330 SPS
#define ADS1220_DR_600SPS           0x5     // 600 SPS
#define ADS1220_DR_1000SPS          0x6     // 1000 SPS

/* 工作模式配置 (CONFIG1[4:3]) */
#define ADS1220_MODE_NORMAL         0x0     // 正常模式
#define ADS1220_MODE_DUTY_CYCLE     0x1     // 占空比模式
#define ADS1220_MODE_TURBO          0x2     // 涡轮模式

/* 转换模式配置 (CONFIG1[2]) */
#define ADS1220_CM_SINGLE           0x0     // 单次转换
#define ADS1220_CM_CONTINUOUS       0x1     // 连续转换

/* 电压基准配置 (CONFIG2[7:6]) */
#define ADS1220_VREF_INTERNAL       0x0     // 内部2.048V基准
#define ADS1220_VREF_REFP_REFN      0x1     // 外部基准REFP/REFN
#define ADS1220_VREF_AVDD_AVSS      0x2     // 外部基准AVDD/AVSS
#define ADS1220_VREF_SUPPLY         0x3     // 供电电压基准

/* 通道枚举 */
typedef enum {
    ADS1220_CHANNEL_AIN0 = 0,
    ADS1220_CHANNEL_AIN1 = 1,
    ADS1220_CHANNEL_AIN2 = 2,
    ADS1220_CHANNEL_AIN3 = 3
} ADS1220_Channel_t;

/* 配置结构体 */
typedef struct {
    uint8_t mux;                // 输入多路复用器
    uint8_t gain;               // 增益设置
    uint8_t pga_bypass;         // PGA旁路
    uint8_t data_rate;          // 数据速率
    uint8_t op_mode;            // 工作模式
    uint8_t conv_mode;          // 转换模式
    uint8_t temp_sensor;        // 温度传感器
    uint8_t burn_out;           // 烧毁检测
    uint8_t vref;               // 电压基准
    uint8_t fir_filter;         // FIR滤波器
    uint8_t low_side_switch;    // 低端开关
    uint8_t idac_current;       // IDAC电流
    uint8_t idac1_routing;      // IDAC1路由
    uint8_t idac2_routing;      // IDAC2路由
    uint8_t drdy_mode;          // DRDY模式
    uint8_t reserved;           // 保留位
} ADS1220_Config_t;

/* 函数声明 */

/**
 * @brief 初始化ADS1220
 * @param hspi SPI句柄指针
 * @retval HAL_StatusTypeDef 初始化状态
 */
HAL_StatusTypeDef ADS1220_Init(SPI_HandleTypeDef *hspi);

/**
 * @brief 复位ADS1220
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_Reset(void);

/**
 * @brief 写入配置到ADS1220
 * @param config 配置结构体指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_WriteConfig(ADS1220_Config_t *config);

/**
 * @brief 写入寄存器
 * @param reg_addr 寄存器地址
 * @param num_regs 寄存器数量
 * @param data 数据指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_WriteRegister(uint8_t reg_addr, uint8_t num_regs, uint8_t *data);

/**
 * @brief 读取寄存器
 * @param reg_addr 寄存器地址
 * @param num_regs 寄存器数量
 * @param data 数据指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_ReadRegister(uint8_t reg_addr, uint8_t num_regs, uint8_t *data);

/**
 * @brief 设置单端输入通道
 * @param channel 通道选择 (ADS1220_CHANNEL_AIN0/1/2/3)
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_SetSingleEndedChannel(ADS1220_Channel_t channel);

/**
 * @brief 启动转换
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_StartConversion(void);

/**
 * @brief 读取ADC数据
 * @param data 数据指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_ReadData(int32_t *data);

/**
 * @brief 等待数据就绪
 * @param timeout_ms 超时时间(毫秒)
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_WaitDataReady(uint32_t timeout_ms);

/**
 * @brief 读取单端输入电压
 * @param channel 通道选择
 * @param voltage 电压值指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_ReadSingleEndedVoltage(ADS1220_Channel_t channel, float *voltage);

/**
 * @brief 验证ADS1220配置是否正确
 * @param expected_config 期望的配置结构体指针
 * @retval HAL_StatusTypeDef 验证结果 (HAL_OK=匹配, HAL_ERROR=不匹配)
 */
HAL_StatusTypeDef ADS1220_VerifyConfig(ADS1220_Config_t *expected_config);

/**
 * @brief 获取当前ADS1220配置
 * @param current_config 当前配置结构体指针
 * @retval HAL_StatusTypeDef 操作状态
 */
HAL_StatusTypeDef ADS1220_GetCurrentConfig(ADS1220_Config_t *current_config);

/**
 * @brief 打印ADS1220配置信息（调试用）
 * @param config 配置结构体指针
 * @param title 标题字符串
 */
void ADS1220_PrintConfig(ADS1220_Config_t *config, const char *title);

#ifdef __cplusplus
}
#endif

#endif /* __ADS1220_H */