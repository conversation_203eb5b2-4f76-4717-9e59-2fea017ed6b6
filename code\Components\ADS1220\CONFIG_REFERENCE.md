# ADS1220 配置验证参考

## 🎯 目标配置

### 期望的寄存器值
```
寄存器0 (CONFIG0): 0x60
寄存器1 (CONFIG1): 0x00  
寄存器2 (CONFIG2): 0x80
寄存器3 (CONFIG3): 0x00
```

## 📋 详细配置解析

### 寄存器0 (CONFIG0) = 0x60
| 位域 | 值 | 含义 |
|------|-----|------|
| MUX[7:4] | 0x6 | AIN0单端输入 (相对AVSS) |
| GAIN[3:1] | 0x0 | 增益1倍 |
| PGA_BYPASS[0] | 0x0 | 启用PGA |

**二进制**: `0110 0000`
- `0110` = AIN0单端输入
- `000` = 增益1倍  
- `0` = 启用PGA

### 寄存器1 (CONFIG1) = 0x00
| 位域 | 值 | 含义 |
|------|-----|------|
| DR[7:5] | 0x0 | 20 SPS采样率 |
| MODE[4:3] | 0x0 | 正常工作模式 |
| CM[2] | 0x0 | 单次转换模式 |
| TS[1] | 0x0 | 禁用温度传感器 |
| BCS[0] | 0x0 | 禁用烧毁检测 |

**二进制**: `0000 0000`

### 寄存器2 (CONFIG2) = 0x80  
| 位域 | 值 | 含义 |
|------|-----|------|
| VREF[7:6] | 0x2 | 外部基准AVDD/AVSS |
| 50/60Hz[5:4] | 0x0 | 无50/60Hz滤波 |
| PSW[3] | 0x0 | 禁用低端开关 |
| IDAC[2:0] | 0x0 | 禁用IDAC |

**二进制**: `1000 0000`
- `10` = 使用AVDD/AVSS作为基准电压

### 寄存器3 (CONFIG3) = 0x00
| 位域 | 值 | 含义 |
|------|-----|------|
| I1MUX[7:5] | 0x0 | IDAC1禁用 |
| I2MUX[4:2] | 0x0 | IDAC2禁用 |
| DRDYM[1] | 0x0 | DRDY仅指示数据就绪 |
| Reserved[0] | 0x0 | 保留位 |

**二进制**: `0000 0000`

## 🔍 验证方法

### 1. RS485命令验证
```bash
command:ads1220_config_check
```

**期望输出**:
```
=== ADS1220配置验证 ===
当前寄存器配置: 0x60 0x00 0x80 0x00
寄存器0解析:
  MUX设置: 0x6 (AIN0单端)
  增益: 1倍
  PGA: 启用
寄存器1解析:
  数据速率: 20 SPS
  工作模式: 正常
  转换模式: 单次
寄存器2解析:
  电压基准: 外部AVDD/AVSS

=== 目标配置对比 ===
期望配置: 0x60 0x00 0x80 0x00
实际配置: 0x60 0x00 0x80 0x00
✓ 所有配置与目标一致！
=== 验证完成 ===
```

### 2. 编程方式验证
```c
// 创建期望配置
ADS1220_Config_t expected_config = {
    .mux = ADS1220_MUX_AIN0_AVSS,      // 0x6
    .gain = ADS1220_GAIN_1,            // 0x0
    .pga_bypass = ADS1220_PGA_ENABLE,  // 0x0
    .data_rate = ADS1220_DR_20SPS,     // 0x0
    .op_mode = ADS1220_MODE_NORMAL,    // 0x0
    .conv_mode = ADS1220_CM_SINGLE,    // 0x0
    .temp_sensor = 0,                  // 0x0
    .burn_out = 0,                     // 0x0
    .vref = ADS1220_VREF_AVDD_AVSS,    // 0x2
    .fir_filter = 0,                   // 0x0
    .low_side_switch = 0,              // 0x0
    .idac_current = 0,                 // 0x0
    .idac1_routing = 0,                // 0x0
    .idac2_routing = 0,                // 0x0
    .drdy_mode = 0,                    // 0x0
    .reserved = 0                      // 0x0
};

// 验证配置
if (ADS1220_VerifyConfig(&expected_config) == HAL_OK) {
    printf("✓ 配置正确\n");
} else {
    printf("✗ 配置错误\n");
}
```

## ⚠️ 常见配置错误

### 1. 寄存器0错误
| 错误值 | 可能原因 | 解决方法 |
|--------|----------|----------|
| 0x80 | 配置为AIN2单端而非AIN0 | 重新设置MUX为0x6 |
| 0x62 | 增益设置为2倍 | 重新设置GAIN为0x0 |
| 0x61 | PGA被旁路 | 重新设置PGA_BYPASS为0x0 |

### 2. 寄存器2错误  
| 错误值 | 可能原因 | 解决方法 |
|--------|----------|----------|
| 0x00 | 使用内部基准 | 重新设置VREF为0x2 |
| 0x40 | 使用REFP/REFN基准 | 重新设置VREF为0x2 |

## 🔧 故障排除

### 配置不匹配时的处理步骤

1. **重新初始化**
   ```bash
   command:ads1220_test
   ```

2. **验证SPI通信**
   - 检查CS引脚连接
   - 确认SPI时钟正常
   - 验证MISO/MOSI连接

3. **检查硬件**
   - 确认供电电压3.3V
   - 检查晶振工作正常
   - 验证复位电路

4. **逐步配置验证**
   ```c
   // 分步验证每个寄存器
   uint8_t reg_data;
   
   // 验证寄存器0
   ADS1220_ReadRegister(0x00, 1, &reg_data);
   if (reg_data != 0x60) {
       printf("寄存器0错误: 0x%02X\n", reg_data);
   }
   
   // 验证寄存器2  
   ADS1220_ReadRegister(0x02, 1, &reg_data);
   if (reg_data != 0x80) {
       printf("寄存器2错误: 0x%02X\n", reg_data);
   }
   ```

## 📊 配置验证清单

- [ ] 寄存器0 = 0x60 (AIN0单端，增益1倍，PGA启用)
- [ ] 寄存器1 = 0x00 (20SPS，正常模式，单次转换)  
- [ ] 寄存器2 = 0x80 (AVDD/AVSS基准)
- [ ] 寄存器3 = 0x00 (默认设置)
- [ ] SPI通信正常
- [ ] 电压读取功能正常
- [ ] 通道切换功能正常

完成所有检查项后，ADS1220应该能够正确工作在单端输入模式。
