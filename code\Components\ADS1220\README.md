# ADS1220 驱动程序使用说明

## 📋 概述

本驱动程序为ADS1220 24位高精度ADC提供完整的单端输入模式支持，专门针对AIN0、AIN1、AIN2三个通道进行了优化配置。

## ⚙️ 硬件配置

### 引脚连接
- **CS (片选)**: PB6
- **DRDY (数据就绪)**: PB6 (与CS共用，可根据实际硬件修改)
- **SPI接口**: SPI3 (PB3-SCK, PB4-MISO, PB5-MOSI)

### 电气特性
- **供电电压**: 3.3V
- **基准电压**: AVDD/AVSS (3.3V)
- **输入范围**: 0 - 3.3V (单端)
- **分辨率**: 24位 (~0.4μV @ 3.3V基准)

## 🔧 软件配置

### 默认配置
- **输入模式**: 单端输入 (AIN0/1/2 相对于AVSS)
- **增益**: 1倍
- **PGA**: 启用
- **采样率**: 20 SPS
- **转换模式**: 单次转换
- **基准**: AVDD/AVSS

## 🚀 使用方法

### 1. 初始化
```c
// 在main函数中初始化
if (ADS1220_Init(&hspi3) == HAL_OK) {
    printf("ADS1220初始化成功\n");
} else {
    printf("ADS1220初始化失败\n");
}
```

### 2. 读取单端电压
```c
float voltage;

// 读取AIN0通道
if (ADS1220_ReadSingleEndedVoltage(ADS1220_CHANNEL_AIN0, &voltage) == HAL_OK) {
    printf("AIN0电压: %.6fV\n", voltage);
}

// 读取AIN1通道
if (ADS1220_ReadSingleEndedVoltage(ADS1220_CHANNEL_AIN1, &voltage) == HAL_OK) {
    printf("AIN1电压: %.6fV\n", voltage);
}

// 读取AIN2通道
if (ADS1220_ReadSingleEndedVoltage(ADS1220_CHANNEL_AIN2, &voltage) == HAL_OK) {
    printf("AIN2电压: %.6fV\n", voltage);
}
```

### 3. 手动控制转换过程
```c
int32_t adc_data;

// 设置通道
ADS1220_SetSingleEndedChannel(ADS1220_CHANNEL_AIN0);

// 启动转换
ADS1220_StartConversion();

// 等待转换完成
if (ADS1220_WaitDataReady(100) == HAL_OK) {
    // 读取原始数据
    if (ADS1220_ReadData(&adc_data) == HAL_OK) {
        // 转换为电压
        float voltage = ((float)adc_data * 3.3f) / 8388608.0f;
        printf("电压: %.6fV (原始数据: %ld)\n", voltage, adc_data);
    }
}
```

## 📡 RS485测试命令

### 1. 初始化测试
```bash
command:ads1220_test
```
**功能**: 初始化ADS1220并显示结果

### 2. 读取所有通道
```bash
command:ads1220_read_ain
```
**功能**: 依次读取AIN0、AIN1、AIN2的电压值

**输出示例**:
```
=== ADS1220单端输入电压读取 ===
AIN0: 1.234567V
AIN1: 2.345678V
AIN2: 0.123456V
=== 读取完成 ===
```

## 🔍 API参考

### 核心函数

#### `ADS1220_Init(SPI_HandleTypeDef *hspi)`
- **功能**: 初始化ADS1220
- **参数**: SPI句柄指针
- **返回**: HAL_StatusTypeDef

#### `ADS1220_ReadSingleEndedVoltage(ADS1220_Channel_t channel, float *voltage)`
- **功能**: 读取指定通道的单端电压
- **参数**: 
  - `channel`: 通道选择 (ADS1220_CHANNEL_AIN0/1/2/3)
  - `voltage`: 电压值指针
- **返回**: HAL_StatusTypeDef

#### `ADS1220_SetSingleEndedChannel(ADS1220_Channel_t channel)`
- **功能**: 设置单端输入通道
- **参数**: 通道选择
- **返回**: HAL_StatusTypeDef

### 通道枚举
```c
typedef enum {
    ADS1220_CHANNEL_AIN0 = 0,  // AIN0通道
    ADS1220_CHANNEL_AIN1 = 1,  // AIN1通道
    ADS1220_CHANNEL_AIN2 = 2,  // AIN2通道
    ADS1220_CHANNEL_AIN3 = 3   // AIN3通道
} ADS1220_Channel_t;
```

## ⚠️ 注意事项

1. **硬件连接**: 确保CS和DRDY引脚连接正确
2. **电压范围**: 输入电压不要超过3.3V
3. **转换时间**: 20SPS模式下每次转换需要约50ms
4. **SPI时序**: 驱动已优化SPI时序，无需额外配置

## 🔧 故障排除

### 常见问题

1. **初始化失败**
   - 检查SPI连接
   - 确认CS引脚配置
   - 验证供电电压

2. **读取数据异常**
   - 检查DRDY引脚连接
   - 确认输入电压范围
   - 验证SPI通信

3. **电压读数不准确**
   - 检查基准电压稳定性
   - 确认增益设置
   - 验证输入阻抗

### 调试建议
- 使用示波器检查SPI信号
- 测量基准电压精度
- 验证输入信号质量

## 📊 性能指标

- **分辨率**: 24位 (有效位数约20位)
- **精度**: ±0.01% (典型值)
- **采样率**: 20 SPS (可配置至1000 SPS)
- **输入阻抗**: >1GΩ
- **功耗**: <1mW (典型值)

## 🔄 版本历史

- **v1.0**: 初始版本，支持AIN0/1/2单端输入
- 完整的寄存器配置支持
- 简洁的API接口
- 详细的错误处理
