#MicroXplorer Configuration settings - do not modify
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_10
ADC1.ContinuousConvMode=ENABLE
ADC1.DMAContinuousRequests=ENABLE
ADC1.IPParameters=Rank-2\#ChannelRegularConversion,master,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,NbrOfConversionFlag,ContinuousConvMode,DMAContinuousRequests
ADC1.NbrOfConversionFlag=1
ADC1.Rank-2\#ChannelRegularConversion=1
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.ADC1.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.0.Instance=DMA2_Stream0
Dma.ADC1.0.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.0.MemInc=DMA_MINC_ENABLE
Dma.ADC1.0.Mode=DMA_CIRCULAR
Dma.ADC1.0.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.0.Priority=DMA_PRIORITY_LOW
Dma.ADC1.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=ADC1
Dma.Request1=SPI3_RX
Dma.Request2=SPI3_TX
Dma.Request3=USART2_RX
Dma.RequestsNb=4
Dma.SPI3_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.SPI3_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.SPI3_RX.1.Instance=DMA1_Stream0
Dma.SPI3_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.SPI3_RX.1.MemInc=DMA_MINC_ENABLE
Dma.SPI3_RX.1.Mode=DMA_NORMAL
Dma.SPI3_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.SPI3_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.SPI3_RX.1.Priority=DMA_PRIORITY_LOW
Dma.SPI3_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.SPI3_TX.2.Direction=DMA_MEMORY_TO_PERIPH
Dma.SPI3_TX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.SPI3_TX.2.Instance=DMA1_Stream7
Dma.SPI3_TX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.SPI3_TX.2.MemInc=DMA_MINC_ENABLE
Dma.SPI3_TX.2.Mode=DMA_NORMAL
Dma.SPI3_TX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.SPI3_TX.2.PeriphInc=DMA_PINC_DISABLE
Dma.SPI3_TX.2.Priority=DMA_PRIORITY_LOW
Dma.SPI3_TX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.3.Instance=DMA1_Stream5
Dma.USART2_RX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.3.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.3.Mode=DMA_NORMAL
Dma.USART2_RX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.3.Priority=DMA_PRIORITY_LOW
Dma.USART2_RX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
FATFS.IPParameters=_CODE_PAGE,_USE_LFN
FATFS._CODE_PAGE=936
FATFS._USE_LFN=3
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Mode=I2C_Fast
I2C1.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.CPN=STM32F427ZGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=DMA
Mcu.IP10=SYS
Mcu.IP11=TIM3
Mcu.IP12=TIM6
Mcu.IP13=USART2
Mcu.IP2=FATFS
Mcu.IP3=I2C1
Mcu.IP4=NVIC
Mcu.IP5=RCC
Mcu.IP6=RTC
Mcu.IP7=SDIO
Mcu.IP8=SPI2
Mcu.IP9=SPI3
Mcu.IPNb=14
Mcu.Name=STM32F427Z(G-I)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PC14/OSC32_IN
Mcu.Pin1=PC15/OSC32_OUT
Mcu.Pin10=PE8
Mcu.Pin11=PE9
Mcu.Pin12=PE10
Mcu.Pin13=PE11
Mcu.Pin14=PE12
Mcu.Pin15=PB12
Mcu.Pin16=PB13
Mcu.Pin17=PB14
Mcu.Pin18=PB15
Mcu.Pin19=PD8
Mcu.Pin2=PH0/OSC_IN
Mcu.Pin20=PD9
Mcu.Pin21=PD10
Mcu.Pin22=PD11
Mcu.Pin23=PD12
Mcu.Pin24=PD13
Mcu.Pin25=PC8
Mcu.Pin26=PC9
Mcu.Pin27=PA13
Mcu.Pin28=PA14
Mcu.Pin29=PC10
Mcu.Pin3=PH1/OSC_OUT
Mcu.Pin30=PC11
Mcu.Pin31=PC12
Mcu.Pin32=PD2
Mcu.Pin33=PB3
Mcu.Pin34=PB4
Mcu.Pin35=PB5
Mcu.Pin36=PB6
Mcu.Pin37=PB8
Mcu.Pin38=PB9
Mcu.Pin39=VP_FATFS_VS_SDIO
Mcu.Pin4=PC0
Mcu.Pin40=VP_RTC_VS_RTC_Activate
Mcu.Pin41=VP_RTC_VS_RTC_Calendar
Mcu.Pin42=VP_SYS_VS_Systick
Mcu.Pin43=VP_TIM3_VS_ClockSourceINT
Mcu.Pin44=VP_TIM6_VS_ClockSourceINT
Mcu.Pin5=PA1
Mcu.Pin6=PA2
Mcu.Pin7=PA3
Mcu.Pin8=PA5
Mcu.Pin9=PE7
Mcu.PinsNb=45
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F427ZGTx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream7_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA1.Locked=true
PA1.Signal=GPIO_Output
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA5.Locked=true
PA5.Signal=ADCx_IN5
PB12.GPIOParameters=GPIO_Speed,PinState
PB12.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PB12.Locked=true
PB12.PinState=GPIO_PIN_SET
PB12.Signal=GPIO_Output
PB13.Locked=true
PB13.Mode=Full_Duplex_Master
PB13.Signal=SPI2_SCK
PB14.Locked=true
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.Locked=true
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB3.Locked=true
PB3.Mode=Full_Duplex_Master
PB3.Signal=SPI3_SCK
PB4.Locked=true
PB4.Mode=Full_Duplex_Master
PB4.Signal=SPI3_MISO
PB5.Locked=true
PB5.Mode=Full_Duplex_Master
PB5.Signal=SPI3_MOSI
PB6.Locked=true
PB6.Signal=GPIO_Output
PB8.Locked=true
PB8.Mode=I2C
PB8.Signal=I2C1_SCL
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PC0.Locked=true
PC0.Signal=ADCx_IN10
PC10.Mode=SD_4_bits_Wide_bus
PC10.Signal=SDIO_D2
PC11.Mode=SD_4_bits_Wide_bus
PC11.Signal=SDIO_D3
PC12.Mode=SD_4_bits_Wide_bus
PC12.Signal=SDIO_CK
PC14/OSC32_IN.Mode=LSE-External-Oscillator
PC14/OSC32_IN.Signal=RCC_OSC32_IN
PC15/OSC32_OUT.Mode=LSE-External-Oscillator
PC15/OSC32_OUT.Signal=RCC_OSC32_OUT
PC8.Mode=SD_4_bits_Wide_bus
PC8.Signal=SDIO_D0
PC9.Mode=SD_4_bits_Wide_bus
PC9.Signal=SDIO_D1
PD10.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD10.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD10.GPIO_PuPd=GPIO_NOPULL
PD10.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD10.Locked=true
PD10.PinState=GPIO_PIN_RESET
PD10.Signal=GPIO_Output
PD11.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD11.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD11.GPIO_PuPd=GPIO_NOPULL
PD11.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD11.Locked=true
PD11.PinState=GPIO_PIN_RESET
PD11.Signal=GPIO_Output
PD12.Locked=true
PD12.Signal=GPIO_Output
PD13.Locked=true
PD13.Signal=GPIO_Output
PD2.Mode=SD_4_bits_Wide_bus
PD2.Signal=SDIO_CMD
PD8.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD8.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD8.GPIO_PuPd=GPIO_NOPULL
PD8.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD8.Locked=true
PD8.PinState=GPIO_PIN_RESET
PD8.Signal=GPIO_Output
PD9.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD9.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD9.GPIO_PuPd=GPIO_NOPULL
PD9.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD9.Locked=true
PD9.PinState=GPIO_PIN_RESET
PD9.Signal=GPIO_Output
PE10.GPIOParameters=GPIO_PuPd,GPIO_Mode
PE10.GPIO_Mode=GPIO_MODE_INPUT
PE10.GPIO_PuPd=GPIO_PULLUP
PE10.Locked=true
PE10.Signal=GPIO_Input
PE11.GPIOParameters=GPIO_PuPd,GPIO_Mode
PE11.GPIO_Mode=GPIO_MODE_INPUT
PE11.GPIO_PuPd=GPIO_PULLUP
PE11.Locked=true
PE11.Signal=GPIO_Input
PE12.GPIOParameters=GPIO_PuPd,GPIO_Mode
PE12.GPIO_Mode=GPIO_MODE_INPUT
PE12.GPIO_PuPd=GPIO_PULLUP
PE12.Locked=true
PE12.Signal=GPIO_Input
PE7.GPIOParameters=GPIO_PuPd,GPIO_Mode
PE7.GPIO_Mode=GPIO_MODE_INPUT
PE7.GPIO_PuPd=GPIO_PULLUP
PE7.Locked=true
PE7.Signal=GPIO_Input
PE8.GPIOParameters=GPIO_PuPd,GPIO_Mode
PE8.GPIO_Mode=GPIO_MODE_INPUT
PE8.GPIO_PuPd=GPIO_PULLUP
PE8.Locked=true
PE8.Signal=GPIO_Input
PE9.GPIOParameters=GPIO_PuPd,GPIO_Mode
PE9.GPIO_Mode=GPIO_MODE_INPUT
PE9.GPIO_PuPd=GPIO_PULLUP
PE9.Locked=true
PE9.Signal=GPIO_Input
PH0/OSC_IN.Mode=HSE-External-Oscillator
PH0/OSC_IN.Signal=RCC_OSC_IN
PH1/OSC_OUT.Mode=HSE-External-Oscillator
PH1/OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F427ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x1000
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=GD32.ioc
ProjectManager.ProjectName=GD32
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x2000
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_ADC1_Init-ADC1-false-HAL-true,5-MX_TIM3_Init-TIM3-false-HAL-true,6-MX_TIM6_Init-TIM6-false-HAL-true,7-MX_I2C1_Init-I2C1-false-HAL-true,8-MX_RTC_Init-RTC-false-HAL-true,9-MX_SPI2_Init-SPI2-false-HAL-true,10-MX_SDIO_SD_Init-SDIO-false-HAL-true,11-MX_FATFS_Init-FATFS-false-HAL-false,12-MX_SPI3_Init-SPI3-false-HAL-true,13-MX_USART2_UART_Init-USART2-false-HAL-true
RCC.48MHZClocksFreq_Value=45000000
RCC.AHBFreq_Value=180000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=45000000
RCC.APB1TimFreq_Value=90000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=90000000
RCC.APB2TimFreq_Value=180000000
RCC.CortexFreq_Value=180000000
RCC.EthernetFreq_Value=180000000
RCC.FCLKCortexFreq_Value=180000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=180000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=160000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQ,PLLQCLKFreq_Value,PLLSourceVirtual,RCC_RTC_Clock_Source,RCC_RTC_Clock_SourceVirtual,RCC_RTC_Clock_Source_FROM_HSE,RTCFreq_Value,RTCHSEDivFreq_Value,SAI_AClocksFreq_Value,SAI_BClocksFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAIOutputFreq_Value,VCOSAIOutputFreq_ValueQ,VcooutputI2S,VcooutputI2SQ
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=180000000
RCC.PLLCLKFreq_Value=180000000
RCC.PLLM=15
RCC.PLLN=216
RCC.PLLQ=8
RCC.PLLQCLKFreq_Value=45000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RCC_RTC_Clock_Source=RCC_RTCCLKSOURCE_LSE
RCC.RCC_RTC_Clock_SourceVirtual=RCC_RTCCLKSOURCE_LSE
RCC.RCC_RTC_Clock_Source_FROM_HSE=RCC_RTCCLKSOURCE_HSE_DIV25
RCC.RTCFreq_Value=32768
RCC.RTCHSEDivFreq_Value=1000000
RCC.SAI_AClocksFreq_Value=20416666.666666668
RCC.SAI_BClocksFreq_Value=20416666.666666668
RCC.SYSCLKFreq_VALUE=180000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=320000000
RCC.VCOInputFreq_Value=1666666.6666666667
RCC.VCOOutputFreq_Value=360000000
RCC.VCOSAIOutputFreq_Value=81666666.66666667
RCC.VCOSAIOutputFreq_ValueQ=20416666.666666668
RCC.VcooutputI2S=160000000
RCC.VcooutputI2SQ=160000000
RTC.Hours=23
RTC.IPParameters=Hours,Minutes,Seconds
RTC.Minutes=55
RTC.Seconds=55
SDIO.ClockDiv=6
SDIO.IPParameters=ClockDiv
SH.ADCx_IN10.0=ADC1_IN10,IN10
SH.ADCx_IN10.ConfNb=1
SH.ADCx_IN5.0=ADC1_IN5
SH.ADCx_IN5.ConfNb=1
SPI2.CLKPhase=SPI_PHASE_2EDGE
SPI2.CLKPolarity=SPI_POLARITY_HIGH
SPI2.CalculateBaudRate=22.5 MBits/s
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,CLKPolarity,CLKPhase
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
SPI3.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_256
SPI3.CLKPhase=SPI_PHASE_2EDGE
SPI3.CalculateBaudRate=175.781 KBits/s
SPI3.Direction=SPI_DIRECTION_2LINES
SPI3.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler,CLKPhase
SPI3.Mode=SPI_MODE_MASTER
SPI3.VirtualType=VM_MASTER
TIM3.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM3.IPParameters=Prescaler,Period,TIM_MasterOutputTrigger,AutoReloadPreload
TIM3.Period=100-1
TIM3.Prescaler=160
TIM3.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
TIM6.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM6.IPParameters=Prescaler,Period,TIM_MasterOutputTrigger,AutoReloadPreload
TIM6.Period=100-1
TIM6.Prescaler=160
TIM6.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
VP_FATFS_VS_SDIO.Mode=SDIO
VP_FATFS_VS_SDIO.Signal=FATFS_VS_SDIO
VP_RTC_VS_RTC_Activate.Mode=RTC_Enabled
VP_RTC_VS_RTC_Activate.Signal=RTC_VS_RTC_Activate
VP_RTC_VS_RTC_Calendar.Mode=RTC_Calendar
VP_RTC_VS_RTC_Calendar.Signal=RTC_VS_RTC_Calendar
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom
