{"name": "GD32", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "startup_stm32f427xx.s"}], "folders": []}, {"name": "User", "files": [], "folders": [{"name": "Core", "files": [{"path": "../Core/Src/main.c"}, {"path": "../Core/Src/gpio.c"}, {"path": "../Core/Src/adc.c"}, {"path": "../Core/Src/dma.c"}, {"path": "../Core/Src/i2c.c"}, {"path": "../Core/Src/rtc.c"}, {"path": "../Core/Src/sdio.c"}, {"path": "../Core/Src/spi.c"}, {"path": "../Core/Src/tim.c"}, {"path": "../Core/Src/usart.c"}, {"path": "../Core/Src/stm32f4xx_it.c"}, {"path": "../Core/Src/stm32f4xx_hal_msp.c"}], "folders": []}, {"name": "FATFS", "files": [], "folders": [{"name": "Target", "files": [{"path": "../FATFS/Target/bsp_driver_sd.c"}, {"path": "../FATFS/Target/sd_diskio.c"}], "folders": []}, {"name": "App", "files": [{"path": "../FATFS/App/fatfs.c"}], "folders": []}]}]}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32F4xx_HAL_Driver", "files": [{"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_sdmmc.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sd.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_mmc.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c"}, {"path": "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Core/Src/system_stm32f4xx.c"}], "folders": []}]}, {"name": "Components", "files": [], "folders": [{"name": "GD25QXX", "files": [{"path": "../Components/GD25QXX/gd25qxx.c"}], "folders": []}, {"name": "ringbuffer", "files": [{"path": "../Components/ringbuffer/ringbuffer.c"}], "folders": []}, {"name": "OLED", "files": [{"path": "../Components/OLED/oled.c"}], "folders": []}, {"name": "ADS1220", "files": [{"path": "../Components/ADS1220/ADS1220.c"}], "folders": []}]}, {"name": "sysFunction", "files": [{"path": "../sysFunction/APP_ADC.c"}, {"path": "../sysFunction/APP_Key.c"}, {"path": "../sysFunction/APP_Led.c"}, {"path": "../sysFunction/APP_OLED.c"}, {"path": "../sysFunction/APP_RTC.c"}, {"path": "../sysFunction/APP_Uart.c"}, {"path": "../sysFunction/Schedular.c"}, {"path": "../sysFunction/APP_FLASH.c"}, {"path": "../sysFunction/APP_EXTSPI.c"}], "folders": []}, {"name": "Middlewares", "files": [], "folders": [{"name": "FatFs", "files": [{"path": "../Middlewares/Third_Party/FatFs/src/diskio.c"}, {"path": "../Middlewares/Third_Party/FatFs/src/ff.c"}, {"path": "../Middlewares/Third_Party/FatFs/src/ff_gen_drv.c"}, {"path": "../Middlewares/Third_Party/FatFs/src/option/syscall.c"}, {"path": "../Middlewares/Third_Party/FatFs/src/option/cc936.c"}], "folders": []}]}, {"name": "::CMSIS", "files": [], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "29cd90b28c9e6b305c9d774079b68b90"}, "targets": {"GD32": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x20000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x10000000", "size": "0x10000"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x80000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null", "archExtensions": ""}, "uploader": "STLink", "uploadConfig": {"bin": "", "proType": "SWD", "resetMode": "default", "runAfterProgram": true, "speed": 4000, "address": "0x8000000", "elFile": "None", "optionBytes": ".eide/gd32.st.option.bytes.ini", "otherCmds": ""}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": ["../Core/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32F4xx/Include", "../Drivers/CMSIS/Include", "../Components/OLED", "../sysFunction", "../Components/ringbuffer", "../Components/GD25QXX", "../FATFS/Target", "../FATFS/App", "../Middlewares/Third_Party/FatFs/src", "../Components/ADS1220", ".cmsis/include", "RTE/_GD32"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32F427xx"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "", "rw-base": ""}}}}}, "version": "3.6"}