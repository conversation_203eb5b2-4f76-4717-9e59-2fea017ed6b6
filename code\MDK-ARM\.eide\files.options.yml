##########################################################################################
#                        Append Compiler Options For Source Files
##########################################################################################

# syntax:
#   <your pattern>: <compiler options>
# For get pattern syntax, please refer to: https://www.npmjs.com/package/micromatch
#
# examples:
#   'main.cpp':           --cpp11 -Og ...
#   'src/*.c':            -gnu -O2 ...
#   'src/lib/**/*.cpp':   --cpp11 -Os ...
#   '!Application/*.c':   -O0
#   '**/*.c':             -O2 -gnu ...

version: "2.1"
options:
    GD32:
        files: {}
        virtualPathFiles:
            <virtual_root>/Application/User/Core/adc.c: ""
            <virtual_root>/Application/User/Core/dma.c: ""
            <virtual_root>/Application/User/Core/i2c.c: ""
            <virtual_root>/Application/User/Core/rtc.c: ""
            <virtual_root>/Application/User/Core/sdio.c: ""
            <virtual_root>/Application/User/Core/spi.c: ""
            <virtual_root>/Application/User/Core/tim.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_adc.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_adc_ex.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_ll_adc.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_i2c.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_i2c_ex.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_rtc.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_rtc_ex.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_ll_sdmmc.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_sd.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_mmc.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_spi.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_tim.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_tim_ex.c: ""
            <virtual_root>/Application/User/FATFS/Target/bsp_driver_sd.c: ""
            <virtual_root>/Application/User/FATFS/Target/sd_diskio.c: ""
            <virtual_root>/Application/User/FATFS/App/fatfs.c: ""
            <virtual_root>/Middlewares/FatFs/diskio.c: ""
            <virtual_root>/Middlewares/FatFs/ff.c: ""
            <virtual_root>/Middlewares/FatFs/ff_gen_drv.c: ""
            <virtual_root>/Middlewares/FatFs/syscall.c: ""
            <virtual_root>/Middlewares/FatFs/cc936.c: ""
