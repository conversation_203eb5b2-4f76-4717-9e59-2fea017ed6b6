--cpu=Cortex-M4.fp.sp
"gd32\startup_stm32f427xx.o"
"gd32\main.o"
"gd32\gpio.o"
"gd32\adc.o"
"gd32\dma.o"
"gd32\i2c.o"
"gd32\rtc.o"
"gd32\sdio.o"
"gd32\spi.o"
"gd32\tim.o"
"gd32\usart.o"
"gd32\stm32f4xx_it.o"
"gd32\stm32f4xx_hal_msp.o"
"gd32\stm32f4xx_hal_adc.o"
"gd32\stm32f4xx_hal_adc_ex.o"
"gd32\stm32f4xx_ll_adc.o"
"gd32\stm32f4xx_hal_rcc.o"
"gd32\stm32f4xx_hal_rcc_ex.o"
"gd32\stm32f4xx_hal_flash.o"
"gd32\stm32f4xx_hal_flash_ex.o"
"gd32\stm32f4xx_hal_flash_ramfunc.o"
"gd32\stm32f4xx_hal_gpio.o"
"gd32\stm32f4xx_hal_dma_ex.o"
"gd32\stm32f4xx_hal_dma.o"
"gd32\stm32f4xx_hal_pwr.o"
"gd32\stm32f4xx_hal_pwr_ex.o"
"gd32\stm32f4xx_hal_cortex.o"
"gd32\stm32f4xx_hal.o"
"gd32\stm32f4xx_hal_exti.o"
"gd32\stm32f4xx_hal_i2c.o"
"gd32\stm32f4xx_hal_i2c_ex.o"
"gd32\stm32f4xx_hal_rtc.o"
"gd32\stm32f4xx_hal_rtc_ex.o"
"gd32\stm32f4xx_ll_sdmmc.o"
"gd32\stm32f4xx_hal_sd.o"
"gd32\stm32f4xx_hal_mmc.o"
"gd32\stm32f4xx_hal_spi.o"
"gd32\stm32f4xx_hal_tim.o"
"gd32\stm32f4xx_hal_tim_ex.o"
"gd32\stm32f4xx_hal_uart.o"
"gd32\gd25qxx.o"
"gd32\system_stm32f4xx.o"
"gd32\ringbuffer.o"
"gd32\oled.o"
"gd32\app_adc.o"
"gd32\app_key.o"
"gd32\app_led.o"
"gd32\app_oled.o"
"gd32\app_rtc.o"
"gd32\app_uart.o"
"gd32\schedular.o"
"gd32\app_flash.o"
"gd32\app_extspi.o"
"gd32\bsp_driver_sd.o"
"gd32\sd_diskio.o"
"gd32\fatfs.o"
"gd32\diskio.o"
"gd32\ff.o"
"gd32\ff_gen_drv.o"
"gd32\syscall.o"
"gd32\cc936.o"
--library_type=microlib --strict --scatter "GD32\GD32.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "GD32.map" -o GD32\GD32.axf