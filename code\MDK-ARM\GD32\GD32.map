Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f427xx.o(RESET) refers to startup_stm32f427xx.o(STACK) for __initial_sp
    startup_stm32f427xx.o(RESET) refers to startup_stm32f427xx.o(.text) for Reset_Handler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream7_IRQHandler) for DMA1_Stream7_IRQHandler
    startup_stm32f427xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f427xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f427xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) for HAL_PWREx_EnableOverDrive
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM6_Init) for MX_TIM6_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to rtc.o(i.MX_RTC_Init) for MX_RTC_Init
    main.o(i.main) refers to spi.o(i.MX_SPI2_Init) for MX_SPI2_Init
    main.o(i.main) refers to sdio.o(i.MX_SDIO_SD_Init) for MX_SDIO_SD_Init
    main.o(i.main) refers to fatfs.o(i.MX_FATFS_Init) for MX_FATFS_Init
    main.o(i.main) refers to spi.o(i.MX_SPI3_Init) for MX_SPI3_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(i.main) refers to app_adc.o(i.adc_dma_init) for adc_dma_init
    main.o(i.main) refers to app_extspi.o(i.ADS1220_Init_AIN0_SingleEnded) for ADS1220_Init_AIN0_SingleEnded
    main.o(i.main) refers to app_uart.o(i.rs485_printf) for rs485_printf
    main.o(i.main) refers to app_flash.o(i.flash_write) for flash_write
    main.o(i.main) refers to strlen.o(.text) for strlen
    main.o(i.main) refers to app_flash.o(i.flash_read) for flash_read
    main.o(i.main) refers to app_flash.o(i.SD_Get_Power_On_Count) for SD_Get_Power_On_Count
    main.o(i.main) refers to app_flash.o(i.SD_Init_Folders) for SD_Init_Folders
    main.o(i.main) refers to printfa.o(i.__0snprintf) for __2snprintf
    main.o(i.main) refers to app_flash.o(i.SD_Increment_Power_On_Count) for SD_Increment_Power_On_Count
    main.o(i.main) refers to app_flash.o(i.Write_Log_With_Timestamp_Universal) for Write_Log_With_Timestamp_Universal
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to schedular.o(i.Schedular_Init) for Schedular_Init
    main.o(i.main) refers to schedular.o(i.Schedular_Run) for Schedular_Run
    main.o(i.main) refers to app_uart.o(.bss) for uart2_ringbuffer_pool
    main.o(i.main) refers to app_uart.o(.bss) for uart2_ringbuffer
    main.o(i.main) refers to app_flash.o(.bss) for read_buffer
    main.o(.data) refers to main.o(.conststring) for .conststring
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    rtc.o(i.HAL_RTC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    rtc.o(i.HAL_RTC_MspInit) refers to stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    rtc.o(i.HAL_RTC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    rtc.o(i.MX_RTC_Init) refers to memseta.o(.text) for __aeabi_memclr4
    rtc.o(i.MX_RTC_Init) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_Init) for HAL_RTC_Init
    rtc.o(i.MX_RTC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    rtc.o(i.MX_RTC_Init) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) for HAL_RTC_SetTime
    rtc.o(i.MX_RTC_Init) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) for HAL_RTC_SetDate
    rtc.o(i.MX_RTC_Init) refers to rtc.o(.bss) for .bss
    sdio.o(i.HAL_SD_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    sdio.o(i.HAL_SD_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    sdio.o(i.HAL_SD_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    sdio.o(i.MX_SDIO_SD_Init) refers to sdio.o(.bss) for .bss
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    spi.o(i.HAL_SPI_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    spi.o(i.HAL_SPI_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.HAL_SPI_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    spi.o(i.HAL_SPI_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.HAL_SPI_MspInit) refers to spi.o(.bss) for .bss
    spi.o(i.MX_SPI2_Init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI2_Init) refers to spi.o(.bss) for .bss
    spi.o(i.MX_SPI3_Init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI3_Init) refers to spi.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM6_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM6_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to app_uart.o(.bss) for uart2_rx_dma_buffer
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to spi.o(.bss) for hdma_spi3_rx
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32f4xx_it.o(i.DMA1_Stream7_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream7_IRQHandler) refers to spi.o(.bss) for hdma_spi3_tx
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_AlarmAEventCallback) for HAL_RTC_AlarmAEventCallback
    stm32f4xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler) refers to stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_AlarmBEventCallback) for HAL_RTCEx_AlarmBEventCallback
    stm32f4xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to rtc.o(i.HAL_RTC_MspDeInit) for HAL_RTC_MspDeInit
    stm32f4xx_hal_rtc.o(i.HAL_RTC_DeactivateAlarm) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.HAL_RTC_GetAlarm) refers to stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) refers to stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) refers to stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_hal_rtc.o(i.HAL_RTC_Init) refers to rtc.o(i.HAL_RTC_MspInit) for HAL_RTC_MspInit
    stm32f4xx_hal_rtc.o(i.HAL_RTC_Init) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_Init) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_PollForAlarmAEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetAlarm) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetAlarm) refers to stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) refers to stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateCoarseCalib) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateCoarseCalib) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateWakeUpTimer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_GetTimeStamp) refers to stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForAlarmBEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper1Event) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper2Event) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTimeStampEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForWakeUpTimerEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetCoarseCalib) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetCoarseCalib) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSmoothCalib) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_TimeStampEventCallback) for HAL_RTCEx_TimeStampEventCallback
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper1EventCallback) for HAL_RTCEx_Tamper1EventCallback
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper2EventCallback) for HAL_RTCEx_Tamper2EventCallback
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerIRQHandler) refers to stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerEventCallback) for HAL_RTCEx_WakeUpTimerEventCallback
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) for SDMMC_GetCmdResp3
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdErase) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdErase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOpCondition) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOpCondition) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) for SDMMC_GetCmdResp3
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7) for SDMMC_GetCmdResp7
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) for SDMMC_GetCmdResp2
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) for SDMMC_GetCmdResp2
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendEXTCSD) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendEXTCSD) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) for SDMMC_GetCmdResp6
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAddMmc) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAddMmc) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSwitch) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSwitch) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_sd.o(i.SD_DMATxAbort) for SD_DMATxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_sd.o(i.SD_DMARxAbort) for SD_DMARxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_hal_sd.o(i.SD_FindSCR) for SD_FindSCR
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) for SDMMC_CmdBusWidth
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_Init) for SDIO_Init
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_DeInit) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_OFF) for SDIO_PowerState_OFF
    stm32f4xx_hal_sd.o(i.HAL_SD_DeInit) refers to sdio.o(i.HAL_SD_MspDeInit) for HAL_SD_MspDeInit
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) for SDMMC_CmdSDEraseStartAdd
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) for SDMMC_CmdSDEraseEndAdd
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdErase) for SDMMC_CmdErase
    stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) for SDMMC_CmdSendStatus
    stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.HAL_SD_GetCardStatus) refers to stm32f4xx_hal_sd.o(i.SD_SendSDStatus) for SD_SendSDStatus
    stm32f4xx_hal_sd.o(i.HAL_SD_GetCardStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to bsp_driver_sd.o(i.HAL_SD_RxCpltCallback) for HAL_SD_RxCpltCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to bsp_driver_sd.o(i.HAL_SD_TxCpltCallback) for HAL_SD_TxCpltCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO) for SDIO_WriteFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.SD_DMATxAbort) for SD_DMATxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.SD_DMARxAbort) for SD_DMARxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_Init) refers to sdio.o(i.HAL_SD_MspInit) for HAL_SD_MspInit
    stm32f4xx_hal_sd.o(i.HAL_SD_Init) refers to stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) for HAL_SD_InitCard
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_Init) for SDIO_Init
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_ON) for SDIO_PowerState_ON
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_hal_sd.o(i.SD_PowerON) for SD_PowerON
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_hal_sd.o(i.SD_InitCard) for SD_InitCard
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) for SD_DMAReceiveCplt
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMAError) for SD_DMAError
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO) for SDIO_WriteFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMATransmitCplt) for SD_DMATransmitCplt
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMAError) for SD_DMAError
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.SD_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_sd.o(i.SD_DMAError) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.SD_DMAError) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMAError) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to bsp_driver_sd.o(i.HAL_SD_RxCpltCallback) for HAL_SD_RxCpltCallback
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) for SDMMC_CmdSendSCR
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetPowerState) for SDIO_GetPowerState
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) for SDMMC_CmdSendCID
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) for SDMMC_CmdSetRelAdd
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) for SDMMC_CmdSendCSD
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCSD) for HAL_SD_GetCardCSD
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) for SDMMC_CmdSelDesel
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_Init) for SDIO_Init
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) for SDMMC_CmdGoIdleState
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) for SDMMC_CmdOperCond
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) for SDMMC_CmdAppOperCommand
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) for SDMMC_CmdStatusRegister
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to app_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to app_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to app_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to app_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to app_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to app_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to app_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to app_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    gd25qxx.o(i.spi_flash_buffer_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_buffer_write) refers to gd25qxx.o(i.spi_flash_page_write) for spi_flash_page_write
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_bulk_erase) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_page_write) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_read_byte) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_read_id) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_read_id) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_read_manufacturer_device_id) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_read_manufacturer_device_id) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_sector_erase) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_send_byte) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    gd25qxx.o(i.spi_flash_send_byte) refers to spi.o(.bss) for hspi2
    gd25qxx.o(i.spi_flash_send_halfword) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    gd25qxx.o(i.spi_flash_send_halfword) refers to spi.o(.bss) for hspi2
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_verify_communication) refers to gd25qxx.o(i.spi_flash_read_id) for spi_flash_read_id
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_write_enable) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_write_enable) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for .data
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_cmd) refers to i2c.o(.bss) for hi2c1
    oled.o(i.OLED_Write_data) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_data) refers to i2c.o(.bss) for hi2c1
    app_adc.o(i.ADC_Data_Storage_Task) refers to app_rtc.o(i.rtc_to_unix_timestamp) for rtc_to_unix_timestamp
    app_adc.o(i.ADC_Data_Storage_Task) refers to f2d.o(.text) for __aeabi_f2d
    app_adc.o(i.ADC_Data_Storage_Task) refers to dmul.o(.text) for __aeabi_dmul
    app_adc.o(i.ADC_Data_Storage_Task) refers to d2f.o(.text) for __aeabi_d2f
    app_adc.o(i.ADC_Data_Storage_Task) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    app_adc.o(i.ADC_Data_Storage_Task) refers to app_flash.o(i.SD_Save_Sample_Data) for SD_Save_Sample_Data
    app_adc.o(i.ADC_Data_Storage_Task) refers to app_flash.o(i.SD_Save_OverLimit_Data) for SD_Save_OverLimit_Data
    app_adc.o(i.ADC_Data_Storage_Task) refers to app_uart.o(.data) for Led_ADC_Mode
    app_adc.o(i.ADC_Data_Storage_Task) refers to app_rtc.o(.bss) for Time
    app_adc.o(i.ADC_Data_Storage_Task) refers to app_rtc.o(.data) for Date
    app_adc.o(i.ADC_Data_Storage_Task) refers to app_adc.o(.data) for .data
    app_adc.o(i.ADC_Proc) refers to app_adc.o(.bss) for .bss
    app_adc.o(i.ADC_Proc) refers to app_adc.o(.data) for .data
    app_adc.o(i.ADC_TASK) refers to memseta.o(.text) for __aeabi_memclr4
    app_adc.o(i.ADC_TASK) refers to app_rtc.o(i.rtc_to_unix_timestamp) for rtc_to_unix_timestamp
    app_adc.o(i.ADC_TASK) refers to app_rtc.o(i.timestamp_to_hex) for timestamp_to_hex
    app_adc.o(i.ADC_TASK) refers to f2d.o(.text) for __aeabi_f2d
    app_adc.o(i.ADC_TASK) refers to dmul.o(.text) for __aeabi_dmul
    app_adc.o(i.ADC_TASK) refers to d2f.o(.text) for __aeabi_d2f
    app_adc.o(i.ADC_TASK) refers to app_adc.o(i.voltage_to_hex) for voltage_to_hex
    app_adc.o(i.ADC_TASK) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    app_adc.o(i.ADC_TASK) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_adc.o(i.ADC_TASK) refers to cdcmple.o(.text) for __aeabi_cdcmple
    app_adc.o(i.ADC_TASK) refers to app_flash.o(i.SD_Save_HideData) for SD_Save_HideData
    app_adc.o(i.ADC_TASK) refers to app_flash.o(i.SD_Save_OverLimit_Data) for SD_Save_OverLimit_Data
    app_adc.o(i.ADC_TASK) refers to app_uart.o(.data) for Led_ADC_Mode
    app_adc.o(i.ADC_TASK) refers to app_led.o(.data) for ucled
    app_adc.o(i.ADC_TASK) refers to app_adc.o(.data) for .data
    app_adc.o(i.ADC_TASK) refers to app_rtc.o(.bss) for Time
    app_adc.o(i.ADC_TASK) refers to app_rtc.o(.data) for Date
    app_adc.o(i.ADC_TASK) refers to app_adc.o(.bss) for .bss
    app_adc.o(i.adc_dma_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    app_adc.o(i.adc_dma_init) refers to app_adc.o(.bss) for .bss
    app_adc.o(i.adc_dma_init) refers to adc.o(.bss) for hadc1
    app_adc.o(i.voltage_to_hex) refers to floorf.o(i.__hardfp_floorf) for __hardfp_floorf
    app_adc.o(i.voltage_to_hex) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    app_adc.o(i.voltage_to_hex) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_key.o(i.Key_Proc) refers to app_key.o(i.Key_Read) for Key_Read
    app_key.o(i.Key_Proc) refers to oled.o(i.OLED_Init) for OLED_Init
    app_key.o(i.Key_Proc) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_key.o(i.Key_Proc) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_key.o(i.Key_Proc) refers to app_flash.o(i.Write_Log_With_Timestamp_Universal) for Write_Log_With_Timestamp_Universal
    app_key.o(i.Key_Proc) refers to schedular.o(i.Update_Sample_Cycle) for Update_Sample_Cycle
    app_key.o(i.Key_Proc) refers to app_key.o(.data) for .data
    app_key.o(i.Key_Proc) refers to app_uart.o(.data) for Led_ADC_Mode
    app_key.o(i.Key_Proc) refers to schedular.o(.data) for sample_cycle
    app_key.o(i.Key_Read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    app_led.o(i.LED_SHINE) refers to app_uart.o(.data) for Led_ADC_Mode
    app_led.o(i.LED_SHINE) refers to app_led.o(.data) for .data
    app_led.o(i.Led_Disp) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    app_led.o(i.Led_Disp) refers to app_led.o(.data) for .data
    app_led.o(i.Led_Proc) refers to app_led.o(i.Led_Disp) for Led_Disp
    app_led.o(i.Led_Proc) refers to app_led.o(.data) for .data
    app_oled.o(i.Oled_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    app_oled.o(i.Oled_Printf) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    app_oled.o(i.oled_task) refers to oled.o(i.OLED_Clear) for OLED_Clear
    app_oled.o(i.oled_task) refers to app_oled.o(i.Oled_Printf) for Oled_Printf
    app_oled.o(i.oled_task) refers to f2d.o(.text) for __aeabi_f2d
    app_oled.o(i.oled_task) refers to dmul.o(.text) for __aeabi_dmul
    app_oled.o(i.oled_task) refers to app_oled.o(.data) for .data
    app_oled.o(i.oled_task) refers to app_uart.o(.data) for Led_ADC_Mode
    app_oled.o(i.oled_task) refers to app_rtc.o(.bss) for Time
    app_oled.o(i.oled_task) refers to app_adc.o(.data) for voltage
    app_rtc.o(i.RTC_Task) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    app_rtc.o(i.RTC_Task) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    app_rtc.o(i.RTC_Task) refers to app_rtc.o(.bss) for .bss
    app_rtc.o(i.RTC_Task) refers to rtc.o(.bss) for hrtc
    app_rtc.o(i.RTC_Task) refers to app_rtc.o(.data) for .data
    app_rtc.o(i.datetime_to_timestamp) refers to app_rtc.o(i.is_leap_year) for is_leap_year
    app_rtc.o(i.datetime_to_timestamp) refers to app_rtc.o(.constdata) for .constdata
    app_rtc.o(i.parse_rtc_datetime_string) refers to _scanf_int.o(.text) for _scanf_int
    app_rtc.o(i.parse_rtc_datetime_string) refers to strchr.o(.text) for strchr
    app_rtc.o(i.parse_rtc_datetime_string) refers to __0sscanf.o(.text) for __0sscanf
    app_rtc.o(i.parse_rtc_datetime_string) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_rtc.o(i.rtc_to_unix_timestamp) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    app_rtc.o(i.rtc_to_unix_timestamp) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    app_rtc.o(i.rtc_to_unix_timestamp) refers to app_rtc.o(i.datetime_to_timestamp) for datetime_to_timestamp
    app_rtc.o(i.timestamp_to_hex) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_uart.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    app_uart.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    app_uart.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    app_uart.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    app_uart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_uart.o(i.HAL_UARTEx_RxEventCallback) refers to app_uart.o(.bss) for .bss
    app_uart.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart2
    app_uart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    app_uart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal.o(.data) for uwTick
    app_uart.o(i.HAL_UART_RxCpltCallback) refers to app_uart.o(.data) for .data
    app_uart.o(i.HAL_UART_RxCpltCallback) refers to app_uart.o(.bss) for .bss
    app_uart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart2
    app_uart.o(i.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    app_uart.o(i.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    app_uart.o(i.RS485_Transmit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    app_uart.o(i.RS485_Transmit) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    app_uart.o(i.RS485_Transmit) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    app_uart.o(i.RS485_Transmit) refers to usart.o(.bss) for huart2
    app_uart.o(i.Uart2_Proc) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    app_uart.o(i.Uart2_Proc) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    app_uart.o(i.Uart2_Proc) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_uart.o(i.Uart2_Proc) refers to app_uart.o(i.parse_uart_command) for parse_uart_command
    app_uart.o(i.Uart2_Proc) refers to app_uart.o(i.prase_rs485_command) for prase_rs485_command
    app_uart.o(i.Uart2_Proc) refers to memseta.o(.text) for __aeabi_memclr4
    app_uart.o(i.Uart2_Proc) refers to app_uart.o(.bss) for .bss
    app_uart.o(i.parse_uart_command) refers to strncmp.o(.text) for strncmp
    app_uart.o(i.parse_uart_command) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_uart.o(i.parse_uart_command) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    app_uart.o(i.parse_uart_command) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    app_uart.o(i.parse_uart_command) refers to app_flash.o(i.Write_Log_With_Timestamp_Universal) for Write_Log_With_Timestamp_Universal
    app_uart.o(i.parse_uart_command) refers to app_uart.o(i.system_selftest) for system_selftest
    app_uart.o(i.parse_uart_command) refers to memseta.o(.text) for __aeabi_memclr4
    app_uart.o(i.parse_uart_command) refers to app_uart.o(.data) for .data
    app_uart.o(i.parse_uart_command) refers to app_rtc.o(.bss) for Time
    app_uart.o(i.parse_uart_command) refers to app_rtc.o(.data) for Date
    app_uart.o(i.parse_uart_command) refers to rtc.o(.bss) for hrtc
    app_uart.o(i.parse_uart_command) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) for HAL_RTC_SetDate
    app_uart.o(i.parse_uart_command) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) for HAL_RTC_SetTime
    app_uart.o(i.parse_uart_command) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_uart.o(i.parse_uart_command) refers to f2d.o(.text) for __aeabi_f2d
    app_uart.o(i.parse_uart_command) refers to app_flash.o(i.SD_Write_Log) for SD_Write_Log
    app_uart.o(i.parse_uart_command) refers to oled.o(i.OLED_Init) for OLED_Init
    app_uart.o(i.parse_uart_command) refers to app_uart.o(i.save_config_to_flash) for save_config_to_flash
    app_uart.o(i.parse_uart_command) refers to app_uart.o(i.read_config_from_flash) for read_config_from_flash
    app_uart.o(i.parse_uart_command) refers to app_flash.o(i.SD_Read_conf_Task) for SD_Read_conf_Task
    app_uart.o(i.parse_uart_command) refers to app_flash.o(i.SD_Init_Folders) for SD_Init_Folders
    app_uart.o(i.parse_uart_command) refers to schedular.o(.data) for sample_cycle
    app_uart.o(i.parse_uart_command) refers to app_rtc.o(i.rtc_to_unix_timestamp) for rtc_to_unix_timestamp
    app_uart.o(i.parse_uart_command) refers to app_flash.o(i.SD_Save_Sample_Data) for SD_Save_Sample_Data
    app_uart.o(i.parse_uart_command) refers to app_flash.o(i.SD_Save_OverLimit_Data) for SD_Save_OverLimit_Data
    app_uart.o(i.parse_uart_command) refers to app_flash.o(i.SD_Reset_Power_On_Count) for SD_Reset_Power_On_Count
    app_uart.o(i.prase_rs485_command) refers to strncmp.o(.text) for strncmp
    app_uart.o(i.prase_rs485_command) refers to memseta.o(.text) for __aeabi_memclr4
    app_uart.o(i.prase_rs485_command) refers to app_rtc.o(i.parse_rtc_datetime_string) for parse_rtc_datetime_string
    app_uart.o(i.prase_rs485_command) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_uart.o(i.prase_rs485_command) refers to gd25qxx.o(i.spi_flash_read_id) for spi_flash_read_id
    app_uart.o(i.prase_rs485_command) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) for HAL_RTC_SetDate
    app_uart.o(i.prase_rs485_command) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) for HAL_RTC_SetTime
    app_uart.o(i.prase_rs485_command) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    app_uart.o(i.prase_rs485_command) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    app_uart.o(i.prase_rs485_command) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_uart.o(i.prase_rs485_command) refers to app_flash.o(i.Write_Log_With_Timestamp_Universal) for Write_Log_With_Timestamp_Universal
    app_uart.o(i.prase_rs485_command) refers to rtc.o(.bss) for hrtc
    app_uart.o(i.prase_rs485_command) refers to app_rtc.o(.bss) for Time
    app_uart.o(i.prase_rs485_command) refers to app_rtc.o(.data) for Date
    app_uart.o(i.read_config_from_flash) refers to scanf_fp.o(.text) for _scanf_real
    app_uart.o(i.read_config_from_flash) refers to app_flash.o(i.flash_read) for flash_read
    app_uart.o(i.read_config_from_flash) refers to __0sscanf.o(.text) for __0sscanf
    app_uart.o(i.read_config_from_flash) refers to f2d.o(.text) for __aeabi_f2d
    app_uart.o(i.read_config_from_flash) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_uart.o(i.read_config_from_flash) refers to app_flash.o(.bss) for read_buffer
    app_uart.o(i.read_config_from_flash) refers to app_uart.o(.data) for .data
    app_uart.o(i.rs485_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    app_uart.o(i.rs485_printf) refers to app_uart.o(i.RS485_Transmit) for RS485_Transmit
    app_uart.o(i.save_config_to_flash) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_uart.o(i.save_config_to_flash) refers to app_flash.o(i.flash_write) for flash_write
    app_uart.o(i.save_config_to_flash) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_uart.o(i.save_config_to_flash) refers to app_uart.o(.data) for .data
    app_uart.o(i.system_selftest) refers to gd25qxx.o(i.spi_flash_init) for spi_flash_init
    app_uart.o(i.system_selftest) refers to gd25qxx.o(i.spi_flash_read_id) for spi_flash_read_id
    app_uart.o(i.system_selftest) refers to gd25qxx.o(i.spi_flash_verify_communication) for spi_flash_verify_communication
    app_uart.o(i.system_selftest) refers to app_flash.o(i.SD_Test) for SD_Test
    app_uart.o(i.system_selftest) refers to app_flash.o(i.SD_Get_Card_Memory_KB) for SD_Get_Card_Memory_KB
    app_uart.o(i.system_selftest) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_uart.o(i.system_selftest) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_uart.o(i.system_selftest) refers to app_flash.o(i.Write_Log_With_Timestamp_Universal) for Write_Log_With_Timestamp_Universal
    app_uart.o(i.system_selftest) refers to app_uart.o(.data) for .data
    app_uart.o(i.system_selftest) refers to app_rtc.o(.bss) for Time
    app_uart.o(i.system_selftest) refers to app_rtc.o(.data) for Date
    schedular.o(i.Schedular_Init) refers to schedular.o(.data) for .data
    schedular.o(i.Schedular_Run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    schedular.o(i.Schedular_Run) refers to schedular.o(.data) for .data
    schedular.o(i.Update_Sample_Cycle) refers to schedular.o(.data) for .data
    schedular.o(.data) refers to app_led.o(i.Led_Proc) for Led_Proc
    schedular.o(.data) refers to app_key.o(i.Key_Proc) for Key_Proc
    schedular.o(.data) refers to app_adc.o(i.ADC_Proc) for ADC_Proc
    schedular.o(.data) refers to app_oled.o(i.oled_task) for oled_task
    schedular.o(.data) refers to app_rtc.o(i.RTC_Task) for RTC_Task
    schedular.o(.data) refers to app_uart.o(i.Uart2_Proc) for Uart2_Proc
    schedular.o(.data) refers to app_adc.o(i.ADC_TASK) for ADC_TASK
    schedular.o(.data) refers to app_led.o(i.LED_SHINE) for LED_SHINE
    schedular.o(.data) refers to app_adc.o(i.ADC_Data_Storage_Task) for ADC_Data_Storage_Task
    app_flash.o(i.Flash_Clear_Log_Cache) refers to app_flash.o(i.Flash_Log_Init) for Flash_Log_Init
    app_flash.o(i.Flash_Clear_Log_Cache) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    app_flash.o(i.Flash_Clear_Log_Cache) refers to gd25qxx.o(i.spi_flash_page_write) for spi_flash_page_write
    app_flash.o(i.Flash_Clear_Log_Cache) refers to app_flash.o(.data) for .data
    app_flash.o(i.Flash_Clear_Log_Cache) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.Flash_Get_Log_Count) refers to app_flash.o(i.Flash_Log_Init) for Flash_Log_Init
    app_flash.o(i.Flash_Get_Log_Count) refers to app_flash.o(.data) for .data
    app_flash.o(i.Flash_Get_Log_Count) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.Flash_Log_Init) refers to gd25qxx.o(i.spi_flash_init) for spi_flash_init
    app_flash.o(i.Flash_Log_Init) refers to gd25qxx.o(i.spi_flash_verify_communication) for spi_flash_verify_communication
    app_flash.o(i.Flash_Log_Init) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    app_flash.o(i.Flash_Log_Init) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    app_flash.o(i.Flash_Log_Init) refers to gd25qxx.o(i.spi_flash_page_write) for spi_flash_page_write
    app_flash.o(i.Flash_Log_Init) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.Flash_Log_Init) refers to app_flash.o(.data) for .data
    app_flash.o(i.Flash_Transfer_Logs_To_SD) refers to app_flash.o(i.Flash_Log_Init) for Flash_Log_Init
    app_flash.o(i.Flash_Transfer_Logs_To_SD) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    app_flash.o(i.Flash_Transfer_Logs_To_SD) refers to app_flash.o(i.SD_Write_Log) for SD_Write_Log
    app_flash.o(i.Flash_Transfer_Logs_To_SD) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    app_flash.o(i.Flash_Transfer_Logs_To_SD) refers to gd25qxx.o(i.spi_flash_page_write) for spi_flash_page_write
    app_flash.o(i.Flash_Transfer_Logs_To_SD) refers to app_flash.o(.data) for .data
    app_flash.o(i.Flash_Transfer_Logs_To_SD) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.Flash_Write_Log) refers to app_flash.o(i.Flash_Log_Init) for Flash_Log_Init
    app_flash.o(i.Flash_Write_Log) refers to strlen.o(.text) for strlen
    app_flash.o(i.Flash_Write_Log) refers to memcpya.o(.text) for __aeabi_memcpy
    app_flash.o(i.Flash_Write_Log) refers to gd25qxx.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    app_flash.o(i.Flash_Write_Log) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    app_flash.o(i.Flash_Write_Log) refers to gd25qxx.o(i.spi_flash_page_write) for spi_flash_page_write
    app_flash.o(i.Flash_Write_Log) refers to app_flash.o(.data) for .data
    app_flash.o(i.Flash_Write_Log) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.Flash_Write_Log_With_Timestamp) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    app_flash.o(i.Flash_Write_Log_With_Timestamp) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    app_flash.o(i.Flash_Write_Log_With_Timestamp) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.Flash_Write_Log_With_Timestamp) refers to app_flash.o(i.Flash_Write_Log) for Flash_Write_Log
    app_flash.o(i.Flash_Write_Log_With_Timestamp) refers to app_rtc.o(.bss) for Time
    app_flash.o(i.Flash_Write_Log_With_Timestamp) refers to rtc.o(.bss) for hrtc
    app_flash.o(i.Flash_Write_Log_With_Timestamp) refers to app_rtc.o(.data) for Date
    app_flash.o(i.SD_Create_New_HideData_File) refers to app_rtc.o(i.rtc_to_unix_timestamp) for rtc_to_unix_timestamp
    app_flash.o(i.SD_Create_New_HideData_File) refers to app_flash.o(i.SD_Generate_DateTime_String) for SD_Generate_DateTime_String
    app_flash.o(i.SD_Create_New_HideData_File) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Create_New_HideData_File) refers to app_rtc.o(.bss) for Time
    app_flash.o(i.SD_Create_New_HideData_File) refers to app_rtc.o(.data) for Date
    app_flash.o(i.SD_Create_New_HideData_File) refers to rtc.o(.bss) for hrtc
    app_flash.o(i.SD_Create_New_HideData_File) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Create_New_OverLimit_File) refers to app_rtc.o(i.rtc_to_unix_timestamp) for rtc_to_unix_timestamp
    app_flash.o(i.SD_Create_New_OverLimit_File) refers to app_flash.o(i.SD_Generate_DateTime_String) for SD_Generate_DateTime_String
    app_flash.o(i.SD_Create_New_OverLimit_File) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Create_New_OverLimit_File) refers to app_rtc.o(.bss) for Time
    app_flash.o(i.SD_Create_New_OverLimit_File) refers to app_rtc.o(.data) for Date
    app_flash.o(i.SD_Create_New_OverLimit_File) refers to rtc.o(.bss) for hrtc
    app_flash.o(i.SD_Create_New_OverLimit_File) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Create_New_Sample_File) refers to app_rtc.o(i.rtc_to_unix_timestamp) for rtc_to_unix_timestamp
    app_flash.o(i.SD_Create_New_Sample_File) refers to app_flash.o(i.SD_Generate_DateTime_String) for SD_Generate_DateTime_String
    app_flash.o(i.SD_Create_New_Sample_File) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Create_New_Sample_File) refers to app_rtc.o(.bss) for Time
    app_flash.o(i.SD_Create_New_Sample_File) refers to app_rtc.o(.data) for Date
    app_flash.o(i.SD_Create_New_Sample_File) refers to rtc.o(.bss) for hrtc
    app_flash.o(i.SD_Create_New_Sample_File) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Generate_DateTime_String) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    app_flash.o(i.SD_Generate_DateTime_String) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    app_flash.o(i.SD_Generate_DateTime_String) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Generate_DateTime_String) refers to app_rtc.o(.bss) for Time
    app_flash.o(i.SD_Generate_DateTime_String) refers to rtc.o(.bss) for hrtc
    app_flash.o(i.SD_Generate_DateTime_String) refers to app_rtc.o(.data) for Date
    app_flash.o(i.SD_Get_Card_Memory_KB) refers to ff.o(i.f_mount) for f_mount
    app_flash.o(i.SD_Get_Card_Memory_KB) refers to ff.o(i.f_getfree) for f_getfree
    app_flash.o(i.SD_Get_Card_Memory_KB) refers to fatfs.o(.data) for SDPath
    app_flash.o(i.SD_Get_Card_Memory_KB) refers to fatfs.o(.bss) for SDFatFS
    app_flash.o(i.SD_Get_Power_On_Count) refers to _scanf_int.o(.text) for _scanf_int
    app_flash.o(i.SD_Get_Power_On_Count) refers to app_flash.o(i.flash_read) for flash_read
    app_flash.o(i.SD_Get_Power_On_Count) refers to __0sscanf.o(.text) for __0sscanf
    app_flash.o(i.SD_Get_Power_On_Count) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Get_Power_On_Count) refers to app_flash.o(i.flash_write) for flash_write
    app_flash.o(i.SD_Get_Power_On_Count) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Increment_Power_On_Count) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Increment_Power_On_Count) refers to app_flash.o(i.flash_write) for flash_write
    app_flash.o(i.SD_Increment_Power_On_Count) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Init_Folders) refers to ff.o(i.f_mount) for f_mount
    app_flash.o(i.SD_Init_Folders) refers to ff.o(i.f_mkdir) for f_mkdir
    app_flash.o(i.SD_Init_Folders) refers to app_flash.o(i.SD_Get_Power_On_Count) for SD_Get_Power_On_Count
    app_flash.o(i.SD_Init_Folders) refers to app_flash.o(i.SD_Init_Log_File) for SD_Init_Log_File
    app_flash.o(i.SD_Init_Folders) refers to fatfs.o(.data) for SDPath
    app_flash.o(i.SD_Init_Folders) refers to fatfs.o(.bss) for SDFatFS
    app_flash.o(i.SD_Init_Folders) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Init_Log_File) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Init_Log_File) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Read_conf_Task) refers to app_flash.o(i.read_config_file) for read_config_file
    app_flash.o(i.SD_Reset_Power_On_Count) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Reset_Power_On_Count) refers to app_flash.o(i.flash_write) for flash_write
    app_flash.o(i.SD_Reset_Power_On_Count) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Save_HideData) refers to app_flash.o(i.SD_Create_New_HideData_File) for SD_Create_New_HideData_File
    app_flash.o(i.SD_Save_HideData) refers to ff.o(i.f_mount) for f_mount
    app_flash.o(i.SD_Save_HideData) refers to ff.o(i.f_open) for f_open
    app_flash.o(i.SD_Save_HideData) refers to ff.o(i.f_lseek) for f_lseek
    app_flash.o(i.SD_Save_HideData) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    app_flash.o(i.SD_Save_HideData) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    app_flash.o(i.SD_Save_HideData) refers to f2d.o(.text) for __aeabi_f2d
    app_flash.o(i.SD_Save_HideData) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Save_HideData) refers to strlen.o(.text) for strlen
    app_flash.o(i.SD_Save_HideData) refers to ff.o(i.f_write) for f_write
    app_flash.o(i.SD_Save_HideData) refers to memseta.o(.text) for __aeabi_memclr4
    app_flash.o(i.SD_Save_HideData) refers to strcpy.o(.text) for strcpy
    app_flash.o(i.SD_Save_HideData) refers to ff.o(i.f_close) for f_close
    app_flash.o(i.SD_Save_HideData) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Save_HideData) refers to fatfs.o(.data) for SDPath
    app_flash.o(i.SD_Save_HideData) refers to fatfs.o(.bss) for SDFatFS
    app_flash.o(i.SD_Save_HideData) refers to app_rtc.o(.bss) for Time
    app_flash.o(i.SD_Save_HideData) refers to rtc.o(.bss) for hrtc
    app_flash.o(i.SD_Save_HideData) refers to app_rtc.o(.data) for Date
    app_flash.o(i.SD_Save_OverLimit_Data) refers to app_flash.o(i.SD_Create_New_OverLimit_File) for SD_Create_New_OverLimit_File
    app_flash.o(i.SD_Save_OverLimit_Data) refers to ff.o(i.f_mount) for f_mount
    app_flash.o(i.SD_Save_OverLimit_Data) refers to ff.o(i.f_open) for f_open
    app_flash.o(i.SD_Save_OverLimit_Data) refers to ff.o(i.f_lseek) for f_lseek
    app_flash.o(i.SD_Save_OverLimit_Data) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    app_flash.o(i.SD_Save_OverLimit_Data) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    app_flash.o(i.SD_Save_OverLimit_Data) refers to f2d.o(.text) for __aeabi_f2d
    app_flash.o(i.SD_Save_OverLimit_Data) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Save_OverLimit_Data) refers to strlen.o(.text) for strlen
    app_flash.o(i.SD_Save_OverLimit_Data) refers to ff.o(i.f_write) for f_write
    app_flash.o(i.SD_Save_OverLimit_Data) refers to ff.o(i.f_close) for f_close
    app_flash.o(i.SD_Save_OverLimit_Data) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Save_OverLimit_Data) refers to fatfs.o(.data) for SDPath
    app_flash.o(i.SD_Save_OverLimit_Data) refers to fatfs.o(.bss) for SDFatFS
    app_flash.o(i.SD_Save_OverLimit_Data) refers to app_rtc.o(.bss) for Time
    app_flash.o(i.SD_Save_OverLimit_Data) refers to rtc.o(.bss) for hrtc
    app_flash.o(i.SD_Save_OverLimit_Data) refers to app_rtc.o(.data) for Date
    app_flash.o(i.SD_Save_OverLimit_Data) refers to app_uart.o(.data) for limit
    app_flash.o(i.SD_Save_Sample_Data) refers to app_flash.o(i.SD_Create_New_Sample_File) for SD_Create_New_Sample_File
    app_flash.o(i.SD_Save_Sample_Data) refers to ff.o(i.f_mount) for f_mount
    app_flash.o(i.SD_Save_Sample_Data) refers to ff.o(i.f_open) for f_open
    app_flash.o(i.SD_Save_Sample_Data) refers to ff.o(i.f_lseek) for f_lseek
    app_flash.o(i.SD_Save_Sample_Data) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    app_flash.o(i.SD_Save_Sample_Data) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    app_flash.o(i.SD_Save_Sample_Data) refers to f2d.o(.text) for __aeabi_f2d
    app_flash.o(i.SD_Save_Sample_Data) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Save_Sample_Data) refers to strlen.o(.text) for strlen
    app_flash.o(i.SD_Save_Sample_Data) refers to ff.o(i.f_write) for f_write
    app_flash.o(i.SD_Save_Sample_Data) refers to ff.o(i.f_close) for f_close
    app_flash.o(i.SD_Save_Sample_Data) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Save_Sample_Data) refers to fatfs.o(.data) for SDPath
    app_flash.o(i.SD_Save_Sample_Data) refers to fatfs.o(.bss) for SDFatFS
    app_flash.o(i.SD_Save_Sample_Data) refers to app_rtc.o(.bss) for Time
    app_flash.o(i.SD_Save_Sample_Data) refers to rtc.o(.bss) for hrtc
    app_flash.o(i.SD_Save_Sample_Data) refers to app_rtc.o(.data) for Date
    app_flash.o(i.SD_Test) refers to ff.o(i.f_mount) for f_mount
    app_flash.o(i.SD_Test) refers to fatfs.o(.data) for SDPath
    app_flash.o(i.SD_Test) refers to fatfs.o(.bss) for SDFatFS
    app_flash.o(i.SD_Write_Log) refers to ff.o(i.f_mount) for f_mount
    app_flash.o(i.SD_Write_Log) refers to ff.o(i.f_open) for f_open
    app_flash.o(i.SD_Write_Log) refers to ff.o(i.f_lseek) for f_lseek
    app_flash.o(i.SD_Write_Log) refers to strlen.o(.text) for strlen
    app_flash.o(i.SD_Write_Log) refers to ff.o(i.f_write) for f_write
    app_flash.o(i.SD_Write_Log) refers to ff.o(i.f_close) for f_close
    app_flash.o(i.SD_Write_Log) refers to fatfs.o(.data) for SDPath
    app_flash.o(i.SD_Write_Log) refers to fatfs.o(.bss) for SDFatFS
    app_flash.o(i.SD_Write_Log) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to printfa.o(i.__0snprintf) for __2snprintf
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to ff.o(i.f_mount) for f_mount
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to ff.o(i.f_open) for f_open
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to ff.o(i.f_lseek) for f_lseek
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to strlen.o(.text) for strlen
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to ff.o(i.f_write) for f_write
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to ff.o(i.f_close) for f_close
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to app_rtc.o(.bss) for Time
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to rtc.o(.bss) for hrtc
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to app_rtc.o(.data) for Date
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to fatfs.o(.data) for SDPath
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to fatfs.o(.bss) for SDFatFS
    app_flash.o(i.SD_Write_Log_With_Timestamp) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.Write_Log_Universal) refers to app_flash.o(i.SD_Write_Log) for SD_Write_Log
    app_flash.o(i.Write_Log_Universal) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.Write_Log_With_Timestamp_Universal) refers to app_flash.o(i.SD_Write_Log_With_Timestamp) for SD_Write_Log_With_Timestamp
    app_flash.o(i.Write_Log_With_Timestamp_Universal) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.flash_read) refers to memseta.o(.text) for __aeabi_memclr4
    app_flash.o(i.flash_read) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    app_flash.o(i.flash_read) refers to app_flash.o(.bss) for .bss
    app_flash.o(i.flash_write) refers to gd25qxx.o(i.spi_flash_init) for spi_flash_init
    app_flash.o(i.flash_write) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    app_flash.o(i.flash_write) refers to strlen.o(.text) for strlen
    app_flash.o(i.flash_write) refers to memseta.o(.text) for __aeabi_memclr4
    app_flash.o(i.flash_write) refers to memcpya.o(.text) for __aeabi_memcpy
    app_flash.o(i.flash_write) refers to gd25qxx.o(i.spi_flash_page_write) for spi_flash_page_write
    app_flash.o(i.read_config_file) refers to strcmp.o(.text) for strcmp
    app_flash.o(i.read_config_file) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_flash.o(i.read_config_file) refers to ff.o(i.f_mount) for f_mount
    app_flash.o(i.read_config_file) refers to ff.o(i.f_open) for f_open
    app_flash.o(i.read_config_file) refers to memseta.o(.text) for __aeabi_memclr4
    app_flash.o(i.read_config_file) refers to ff.o(i.f_read) for f_read
    app_flash.o(i.read_config_file) refers to strtok.o(.text) for strtok
    app_flash.o(i.read_config_file) refers to strlen.o(.text) for strlen
    app_flash.o(i.read_config_file) refers to strstr.o(.text) for strstr
    app_flash.o(i.read_config_file) refers to strchr.o(.text) for strchr
    app_flash.o(i.read_config_file) refers to strncpy.o(.text) for strncpy
    app_flash.o(i.read_config_file) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    app_flash.o(i.read_config_file) refers to d2f.o(.text) for __aeabi_d2f
    app_flash.o(i.read_config_file) refers to f2d.o(.text) for __aeabi_f2d
    app_flash.o(i.read_config_file) refers to ff.o(i.f_close) for f_close
    app_flash.o(i.read_config_file) refers to fatfs.o(.data) for SDPath
    app_flash.o(i.read_config_file) refers to fatfs.o(.bss) for SDFatFS
    app_extspi.o(i.ADS1220_CS_HIGH) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    app_extspi.o(i.ADS1220_CS_LOW) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    app_extspi.o(i.ADS1220_Init_AIN0_SingleEnded) refers to app_extspi.o(i.ADS1220_SendCommand) for ADS1220_SendCommand
    app_extspi.o(i.ADS1220_Init_AIN0_SingleEnded) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    app_extspi.o(i.ADS1220_Init_AIN0_SingleEnded) refers to app_extspi.o(i.ADS1220_WriteRegister) for ADS1220_WriteRegister
    app_extspi.o(i.ADS1220_Init_AIN0_SingleEnded) refers to app_extspi.o(i.ADS1220_ReadRegister) for ADS1220_ReadRegister
    app_extspi.o(i.ADS1220_Init_AIN0_SingleEnded) refers to app_uart.o(i.rs485_printf) for rs485_printf
    app_extspi.o(i.ADS1220_ReadRegister) refers to app_extspi.o(i.ADS1220_CS_LOW) for ADS1220_CS_LOW
    app_extspi.o(i.ADS1220_ReadRegister) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    app_extspi.o(i.ADS1220_ReadRegister) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    app_extspi.o(i.ADS1220_ReadRegister) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    app_extspi.o(i.ADS1220_ReadRegister) refers to app_extspi.o(i.ADS1220_CS_HIGH) for ADS1220_CS_HIGH
    app_extspi.o(i.ADS1220_ReadRegister) refers to spi.o(.bss) for hspi3
    app_extspi.o(i.ADS1220_SendCommand) refers to app_extspi.o(i.ADS1220_CS_LOW) for ADS1220_CS_LOW
    app_extspi.o(i.ADS1220_SendCommand) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    app_extspi.o(i.ADS1220_SendCommand) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    app_extspi.o(i.ADS1220_SendCommand) refers to app_extspi.o(i.ADS1220_CS_HIGH) for ADS1220_CS_HIGH
    app_extspi.o(i.ADS1220_SendCommand) refers to spi.o(.bss) for hspi3
    app_extspi.o(i.ADS1220_TestSPI) refers to app_extspi.o(i.ADS1220_Init_AIN0_SingleEnded) for ADS1220_Init_AIN0_SingleEnded
    app_extspi.o(i.ADS1220_WriteRegister) refers to app_extspi.o(i.ADS1220_CS_LOW) for ADS1220_CS_LOW
    app_extspi.o(i.ADS1220_WriteRegister) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    app_extspi.o(i.ADS1220_WriteRegister) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    app_extspi.o(i.ADS1220_WriteRegister) refers to app_extspi.o(i.ADS1220_CS_HIGH) for ADS1220_CS_HIGH
    app_extspi.o(i.ADS1220_WriteRegister) refers to spi.o(.bss) for hspi3
    bsp_driver_sd.o(i.BSP_SD_Erase) refers to stm32f4xx_hal_sd.o(i.HAL_SD_Erase) for HAL_SD_Erase
    bsp_driver_sd.o(i.BSP_SD_Erase) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_GetCardInfo) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardInfo) for HAL_SD_GetCardInfo
    bsp_driver_sd.o(i.BSP_SD_GetCardInfo) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_GetCardState) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    bsp_driver_sd.o(i.BSP_SD_GetCardState) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_Init) refers to bsp_driver_sd.o(i.BSP_SD_IsDetected) for BSP_SD_IsDetected
    bsp_driver_sd.o(i.BSP_SD_Init) refers to stm32f4xx_hal_sd.o(i.HAL_SD_Init) for HAL_SD_Init
    bsp_driver_sd.o(i.BSP_SD_Init) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) for HAL_SD_ConfigWideBusOperation
    bsp_driver_sd.o(i.BSP_SD_Init) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) for HAL_SD_ReadBlocks
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) for HAL_SD_ReadBlocks_DMA
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks_DMA) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks) refers to stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) for HAL_SD_WriteBlocks
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) for HAL_SD_WriteBlocks_DMA
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks_DMA) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.HAL_SD_AbortCallback) refers to bsp_driver_sd.o(i.BSP_SD_AbortCallback) for BSP_SD_AbortCallback
    bsp_driver_sd.o(i.HAL_SD_RxCpltCallback) refers to bsp_driver_sd.o(i.BSP_SD_ReadCpltCallback) for BSP_SD_ReadCpltCallback
    bsp_driver_sd.o(i.HAL_SD_TxCpltCallback) refers to bsp_driver_sd.o(i.BSP_SD_WriteCpltCallback) for BSP_SD_WriteCpltCallback
    sd_diskio.o(i.SD_CheckStatus) refers to bsp_driver_sd.o(i.BSP_SD_GetCardState) for BSP_SD_GetCardState
    sd_diskio.o(i.SD_CheckStatus) refers to sd_diskio.o(.data) for .data
    sd_diskio.o(i.SD_initialize) refers to bsp_driver_sd.o(i.BSP_SD_Init) for BSP_SD_Init
    sd_diskio.o(i.SD_initialize) refers to sd_diskio.o(i.SD_CheckStatus) for SD_CheckStatus
    sd_diskio.o(i.SD_initialize) refers to sd_diskio.o(.data) for .data
    sd_diskio.o(i.SD_ioctl) refers to bsp_driver_sd.o(i.BSP_SD_GetCardInfo) for BSP_SD_GetCardInfo
    sd_diskio.o(i.SD_ioctl) refers to sd_diskio.o(.data) for .data
    sd_diskio.o(i.SD_read) refers to bsp_driver_sd.o(i.BSP_SD_ReadBlocks) for BSP_SD_ReadBlocks
    sd_diskio.o(i.SD_read) refers to bsp_driver_sd.o(i.BSP_SD_GetCardState) for BSP_SD_GetCardState
    sd_diskio.o(i.SD_status) refers to sd_diskio.o(i.SD_CheckStatus) for SD_CheckStatus
    sd_diskio.o(i.SD_write) refers to bsp_driver_sd.o(i.BSP_SD_WriteBlocks) for BSP_SD_WriteBlocks
    sd_diskio.o(i.SD_write) refers to bsp_driver_sd.o(i.BSP_SD_GetCardState) for BSP_SD_GetCardState
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_initialize) for SD_initialize
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_status) for SD_status
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_read) for SD_read
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_write) for SD_write
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_ioctl) for SD_ioctl
    fatfs.o(i.MX_FATFS_Init) refers to ff_gen_drv.o(i.FATFS_LinkDriver) for FATFS_LinkDriver
    fatfs.o(i.MX_FATFS_Init) refers to fatfs.o(.data) for .data
    fatfs.o(i.MX_FATFS_Init) refers to sd_diskio.o(.constdata) for SD_Driver
    diskio.o(i.disk_initialize) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_ioctl) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_read) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_status) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_write) refers to ff_gen_drv.o(.bss) for disk
    ff.o(i.check_fs) refers to ff.o(i.move_window) for move_window
    ff.o(i.check_fs) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.check_fs) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.chk_lock) refers to ff.o(.bss) for .bss
    ff.o(i.clear_lock) refers to ff.o(.bss) for .bss
    ff.o(i.cmp_lfn) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.cmp_lfn) refers to cc936.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.cmp_lfn) refers to ff.o(.constdata) for .constdata
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to cc936.o(i.ff_convert) for ff_convert
    ff.o(i.create_name) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.create_name) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_name) refers to cc936.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.dec_lock) refers to ff.o(.bss) for .bss
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.cmp_lfn) for cmp_lfn
    ff.o(i.dir_find) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.dir_next) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_next) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.pick_lfn) for pick_lfn
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_read) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.dir_register) refers to ff.o(i.gen_numname) for gen_numname
    ff.o(i.dir_register) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.st_word) for st_word
    ff.o(i.dir_register) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_register) refers to ff.o(.constdata) for .constdata
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_remove) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_close) refers to ff.o(i.validate) for validate
    ff.o(i.f_close) refers to ff.o(i.dec_lock) for dec_lock
    ff.o(i.f_closedir) refers to ff.o(i.validate) for validate
    ff.o(i.f_closedir) refers to ff.o(i.dec_lock) for dec_lock
    ff.o(i.f_getfree) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_getfree) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_getfree) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.f_gets) refers to ff.o(i.f_read) for f_read
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_lseek) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_mkdir) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.f_mkdir) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_mkdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkdir) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_mkdir) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_mkdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_mkdir) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_mkfs) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.f_mkfs) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkfs) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkfs) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_mkfs) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_mkfs) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_mkfs) refers to ff.o(.data) for .data
    ff.o(i.f_mkfs) refers to ff.o(.constdata) for .constdata
    ff.o(i.f_mount) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_mount) refers to ff.o(i.clear_lock) for clear_lock
    ff.o(i.f_mount) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_mount) refers to ff.o(.data) for .data
    ff.o(i.f_open) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_open) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.chk_lock) for chk_lock
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_open) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_open) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_open) refers to ff.o(i.inc_lock) for inc_lock
    ff.o(i.f_open) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_open) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_open) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_open) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_open) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_open) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_open) refers to ff.o(.bss) for .bss
    ff.o(i.f_opendir) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_opendir) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_opendir) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_opendir) refers to ff.o(i.inc_lock) for inc_lock
    ff.o(i.f_printf) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_printf) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_putc) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_putc) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_puts) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_puts) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_readdir) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_rename) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_rename) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_rename) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to ff.o(i.chk_lock) for chk_lock
    ff.o(i.f_rename) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_rename) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_rename) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_stat) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_stat) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_stat) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_sync) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_sync) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_sync) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_unlink) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_unlink) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.chk_lock) for chk_lock
    ff.o(i.f_unlink) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_unlink) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_write) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.find_volume) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.find_volume) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.find_volume) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.find_volume) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.find_volume) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.find_volume) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.find_volume) refers to ff.o(i.move_window) for move_window
    ff.o(i.find_volume) refers to ff.o(i.clear_lock) for clear_lock
    ff.o(i.find_volume) refers to ff.o(.data) for .data
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.follow_path) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.gen_numname) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.get_fat) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.get_fat) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.get_fileinfo) refers to cc936.o(i.ff_convert) for ff_convert
    ff.o(i.get_fileinfo) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.inc_lock) refers to ff.o(.bss) for .bss
    ff.o(i.ld_clust) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.move_window) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.pick_lfn) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.pick_lfn) refers to ff.o(.constdata) for .constdata
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.put_fat) refers to ff.o(i.st_word) for st_word
    ff.o(i.put_fat) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.put_fat) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.putc_bfd) refers to ff.o(i.f_write) for f_write
    ff.o(i.putc_flush) refers to ff.o(i.f_write) for f_write
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.st_clust) refers to ff.o(i.st_word) for st_word
    ff.o(i.sync_fs) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.sync_fs) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.sync_fs) refers to ff.o(i.st_word) for st_word
    ff.o(i.sync_fs) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.sync_fs) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync_fs) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.sync_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    ff_gen_drv.o(i.FATFS_GetAttachedDriversNbr) refers to ff_gen_drv.o(.bss) for .bss
    ff_gen_drv.o(i.FATFS_LinkDriver) refers to ff_gen_drv.o(i.FATFS_LinkDriverEx) for FATFS_LinkDriverEx
    ff_gen_drv.o(i.FATFS_LinkDriverEx) refers to ff_gen_drv.o(.bss) for .bss
    ff_gen_drv.o(i.FATFS_UnLinkDriver) refers to ff_gen_drv.o(i.FATFS_UnLinkDriverEx) for FATFS_UnLinkDriverEx
    ff_gen_drv.o(i.FATFS_UnLinkDriverEx) refers to ff_gen_drv.o(.bss) for .bss
    syscall.o(i.ff_memalloc) refers to malloc.o(i.malloc) for malloc
    syscall.o(i.ff_memfree) refers to malloc.o(i.free) for free
    cc936.o(i.ff_convert) refers to cc936.o(.constdata) for .constdata
    cc936.o(i.ff_wtoupper) refers to cc936.o(.constdata) for .constdata
    atof.o(i.__hardfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__hardfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.__softfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__softfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers to errno.o(i.__set_errno) for __set_errno
    floorf.o(i.__hardfp_floorf) refers (Special) to iusefp.o(.text) for __I$use$fp
    floorf.o(i.__softfp_floorf) refers (Special) to iusefp.o(.text) for __I$use$fp
    floorf.o(i.floorf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(.text) for _frnd
    roundf.o(i.roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f427xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f427xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f427xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f427xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f427xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f427xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_c.o(.text) for isspace
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_c.o(.text) for isspace
    frnd.o(.text) refers to fepilogue.o(.text) for _float_round
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.bss), (1024 bytes).
    Removing main.o(.bss), (564 bytes).
    Removing main.o(.bss), (560 bytes).
    Removing main.o(.bss), (32 bytes).
    Removing main.o(.bss), (100 bytes).
    Removing main.o(.conststring), (12 bytes).
    Removing main.o(.data), (91 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (4 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (68 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (56 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(.rrx_text), (6 bytes).
    Removing rtc.o(i.HAL_RTC_MspDeInit), (24 bytes).
    Removing rtc.o(.data), (12 bytes).
    Removing sdio.o(.rev16_text), (4 bytes).
    Removing sdio.o(.revsh_text), (4 bytes).
    Removing sdio.o(.rrx_text), (6 bytes).
    Removing sdio.o(i.HAL_SD_MspDeInit), (60 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.HAL_SPI_MspDeInit), (92 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (48 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (60 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (112 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (302 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (170 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (118 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start), (252 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (108 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (90 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (448 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (58 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (144 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (220 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (228 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (86 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (84 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (272 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (96 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (112 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI), (112 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (132 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (360 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (204 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (112 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (116 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBGetConfig), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram), (188 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (156 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (184 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_GetBank2WRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP), (24 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (396 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (176 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive), (112 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterUnderDriveSTOPMode), (96 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (300 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (516 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (156 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (280 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (112 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rtc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_AlarmAEventCallback), (2 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler), (88 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_DST_Add1Hour), (30 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_DST_ClearStoreOperation), (30 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_DST_ReadStoreOperation), (10 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_DST_SetStoreOperation), (30 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_DST_Sub1Hour), (30 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_DeInit), (132 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_DeactivateAlarm), (178 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_GetAlarm), (124 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_GetState), (4 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_PollForAlarmAEvent), (66 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_SetAlarm), (364 bytes).
    Removing stm32f4xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT), (416 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_AlarmBEventCallback), (2 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPRead), (10 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPWrite), (10 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateCalibrationOutPut), (56 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateCoarseCalib), (76 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock), (76 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateTamper), (38 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateTimeStamp), (70 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateWakeUpTimer), (122 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DisableBypassShadow), (56 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_EnableBypassShadow), (56 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_GetTimeStamp), (148 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_GetWakeUpTimer), (8 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForAlarmBEvent), (66 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper1Event), (66 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper2Event), (66 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTimeStampEvent), (82 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForWakeUpTimerEvent), (66 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetCalibrationOutPut), (76 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetCoarseCalib), (90 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock), (76 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSmoothCalib), (130 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift), (154 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTamper), (92 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTamper_IT), (120 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTimeStamp), (118 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTimeStamp_IT), (156 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer), (210 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT), (260 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper1EventCallback), (2 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper2EventCallback), (2 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler), (120 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_TimeStampEventCallback), (2 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerEventCallback), (2 bytes).
    Removing stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerIRQHandler), (48 bytes).
    Removing stm32f4xx_ll_sdmmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_sdmmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_sdmmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDIO_GetCommandResponse), (6 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDIO_GetDataCounter), (4 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDIO_GetFIFOCount), (6 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_OFF), (8 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDIO_SetSDMMCReadWaitMode), (14 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdErase), (48 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseEndAdd), (50 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseStartAdd), (50 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOpCondition), (44 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd), (50 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd), (50 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendEXTCSD), (50 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAddMmc), (52 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister), (48 bytes).
    Removing stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSwitch), (50 bytes).
    Removing stm32f4xx_hal_sd.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sd.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sd.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_Abort), (120 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_AbortCallback), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT), (156 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_DeInit), (38 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_Erase), (196 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCID), (80 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_GetCardStatus), (168 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_GetError), (4 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_GetState), (6 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler), (520 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA), (292 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT), (188 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA), (288 bytes).
    Removing stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT), (180 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_DMAError), (100 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt), (66 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_DMARxAbort), (68 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_DMATransmitCplt), (14 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_DMATxAbort), (68 bytes).
    Removing stm32f4xx_hal_sd.o(i.SD_SendSDStatus), (240 bytes).
    Removing stm32f4xx_hal_mmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_mmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_mmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort), (292 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (292 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop), (66 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (252 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (232 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (172 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (272 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (156 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (204 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (144 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR), (88 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR), (28 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (144 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR), (76 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR), (124 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (106 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (102 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt), (100 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (90 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (116 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT), (32 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (304 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig), (80 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing gd25qxx.o(.rev16_text), (4 bytes).
    Removing gd25qxx.o(.revsh_text), (4 bytes).
    Removing gd25qxx.o(.rrx_text), (6 bytes).
    Removing gd25qxx.o(i.spi_flash_buffer_write), (188 bytes).
    Removing gd25qxx.o(i.spi_flash_bulk_erase), (52 bytes).
    Removing gd25qxx.o(i.spi_flash_read_byte), (6 bytes).
    Removing gd25qxx.o(i.spi_flash_read_manufacturer_device_id), (84 bytes).
    Removing gd25qxx.o(i.spi_flash_send_halfword), (32 bytes).
    Removing gd25qxx.o(i.spi_flash_start_read_sequence), (52 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (124 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (74 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (78 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (170 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (80 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (102 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (16 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (52 bytes).
    Removing oled.o(i.OLED_Display_Off), (24 bytes).
    Removing oled.o(i.OLED_Display_On), (24 bytes).
    Removing oled.o(i.OLED_ShowFloat), (266 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (76 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (136 bytes).
    Removing oled.o(i.OLED_ShowNum), (114 bytes).
    Removing oled.o(i.OLED_ShowPic), (64 bytes).
    Removing app_adc.o(.rev16_text), (4 bytes).
    Removing app_adc.o(.revsh_text), (4 bytes).
    Removing app_adc.o(.rrx_text), (6 bytes).
    Removing app_key.o(.rev16_text), (4 bytes).
    Removing app_key.o(.revsh_text), (4 bytes).
    Removing app_key.o(.rrx_text), (6 bytes).
    Removing app_key.o(.data), (1 bytes).
    Removing app_led.o(.rev16_text), (4 bytes).
    Removing app_led.o(.revsh_text), (4 bytes).
    Removing app_led.o(.rrx_text), (6 bytes).
    Removing app_oled.o(.rev16_text), (4 bytes).
    Removing app_oled.o(.revsh_text), (4 bytes).
    Removing app_oled.o(.rrx_text), (6 bytes).
    Removing app_rtc.o(.rev16_text), (4 bytes).
    Removing app_rtc.o(.revsh_text), (4 bytes).
    Removing app_rtc.o(.rrx_text), (6 bytes).
    Removing app_uart.o(.rev16_text), (4 bytes).
    Removing app_uart.o(.revsh_text), (4 bytes).
    Removing app_uart.o(.rrx_text), (6 bytes).
    Removing app_uart.o(.data), (1 bytes).
    Removing app_uart.o(.data), (6 bytes).
    Removing app_uart.o(.data), (8 bytes).
    Removing schedular.o(.rev16_text), (4 bytes).
    Removing schedular.o(.revsh_text), (4 bytes).
    Removing schedular.o(.rrx_text), (6 bytes).
    Removing app_flash.o(.rev16_text), (4 bytes).
    Removing app_flash.o(.revsh_text), (4 bytes).
    Removing app_flash.o(.rrx_text), (6 bytes).
    Removing app_flash.o(i.Flash_Clear_Log_Cache), (92 bytes).
    Removing app_flash.o(i.Flash_Get_Log_Count), (32 bytes).
    Removing app_flash.o(i.Flash_Log_Init), (92 bytes).
    Removing app_flash.o(i.Flash_Transfer_Logs_To_SD), (156 bytes).
    Removing app_flash.o(i.Flash_Write_Log), (132 bytes).
    Removing app_flash.o(i.Flash_Write_Log_With_Timestamp), (128 bytes).
    Removing app_flash.o(i.Write_Log_Universal), (20 bytes).
    Removing app_flash.o(.data), (1 bytes).
    Removing app_extspi.o(.rev16_text), (4 bytes).
    Removing app_extspi.o(.revsh_text), (4 bytes).
    Removing app_extspi.o(.rrx_text), (6 bytes).
    Removing app_extspi.o(i.ADS1220_TestSPI), (4 bytes).
    Removing bsp_driver_sd.o(.rev16_text), (4 bytes).
    Removing bsp_driver_sd.o(.revsh_text), (4 bytes).
    Removing bsp_driver_sd.o(.rrx_text), (6 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_AbortCallback), (2 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_DetectIT), (2 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_Erase), (28 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_ITConfig), (4 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_ReadBlocks_DMA), (28 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_ReadCpltCallback), (2 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_WriteBlocks_DMA), (28 bytes).
    Removing bsp_driver_sd.o(i.BSP_SD_WriteCpltCallback), (2 bytes).
    Removing bsp_driver_sd.o(i.HAL_SD_AbortCallback), (8 bytes).
    Removing bsp_driver_sd.o(i.HAL_SD_RxCpltCallback), (8 bytes).
    Removing bsp_driver_sd.o(i.HAL_SD_TxCpltCallback), (8 bytes).
    Removing sd_diskio.o(.rev16_text), (4 bytes).
    Removing sd_diskio.o(.revsh_text), (4 bytes).
    Removing sd_diskio.o(.rrx_text), (6 bytes).
    Removing fatfs.o(.rev16_text), (4 bytes).
    Removing fatfs.o(.revsh_text), (4 bytes).
    Removing fatfs.o(.rrx_text), (6 bytes).
    Removing fatfs.o(.bss), (560 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).
    Removing diskio.o(.rrx_text), (6 bytes).
    Removing diskio.o(i.get_fattime), (4 bytes).
    Removing ff.o(.rev16_text), (4 bytes).
    Removing ff.o(.revsh_text), (4 bytes).
    Removing ff.o(.rrx_text), (6 bytes).
    Removing ff.o(i.dir_read), (182 bytes).
    Removing ff.o(i.dir_remove), (78 bytes).
    Removing ff.o(i.f_closedir), (34 bytes).
    Removing ff.o(i.f_gets), (72 bytes).
    Removing ff.o(i.f_mkfs), (1356 bytes).
    Removing ff.o(i.f_opendir), (172 bytes).
    Removing ff.o(i.f_printf), (460 bytes).
    Removing ff.o(i.f_putc), (30 bytes).
    Removing ff.o(i.f_puts), (38 bytes).
    Removing ff.o(i.f_readdir), (102 bytes).
    Removing ff.o(i.f_rename), (292 bytes).
    Removing ff.o(i.f_stat), (88 bytes).
    Removing ff.o(i.f_truncate), (162 bytes).
    Removing ff.o(i.f_unlink), (192 bytes).
    Removing ff.o(i.get_fileinfo), (244 bytes).
    Removing ff.o(i.pick_lfn), (116 bytes).
    Removing ff.o(i.putc_bfd), (70 bytes).
    Removing ff.o(i.putc_flush), (42 bytes).
    Removing ff_gen_drv.o(.rev16_text), (4 bytes).
    Removing ff_gen_drv.o(.revsh_text), (4 bytes).
    Removing ff_gen_drv.o(.rrx_text), (6 bytes).
    Removing ff_gen_drv.o(i.FATFS_GetAttachedDriversNbr), (12 bytes).
    Removing ff_gen_drv.o(i.FATFS_UnLinkDriver), (6 bytes).
    Removing ff_gen_drv.o(i.FATFS_UnLinkDriverEx), (52 bytes).
    Removing syscall.o(.rev16_text), (4 bytes).
    Removing syscall.o(.revsh_text), (4 bytes).
    Removing syscall.o(.rrx_text), (6 bytes).
    Removing cc936.o(.rev16_text), (4 bytes).
    Removing cc936.o(.revsh_text), (4 bytes).
    Removing cc936.o(.rrx_text), (6 bytes).

827 unused section(s) (total 66355 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/rtc.c                        0x00000000   Number         0  rtc.o ABSOLUTE
    ../Core/Src/sdio.c                       0x00000000   Number         0  sdio.o ABSOLUTE
    ../Core/Src/spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_mmc.c 0x00000000   Number         0  stm32f4xx_hal_mmc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c 0x00000000   Number         0  stm32f4xx_hal_rtc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rtc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sd.c 0x00000000   Number         0  stm32f4xx_hal_sd.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_sdmmc.c 0x00000000   Number         0  stm32f4xx_ll_sdmmc.o ABSOLUTE
    ../FATFS/App/fatfs.c                     0x00000000   Number         0  fatfs.o ABSOLUTE
    ../FATFS/Target/bsp_driver_sd.c          0x00000000   Number         0  bsp_driver_sd.o ABSOLUTE
    ../FATFS/Target/sd_diskio.c              0x00000000   Number         0  sd_diskio.o ABSOLUTE
    ../Middlewares/Third_Party/FatFs/src/diskio.c 0x00000000   Number         0  diskio.o ABSOLUTE
    ../Middlewares/Third_Party/FatFs/src/ff.c 0x00000000   Number         0  ff.o ABSOLUTE
    ../Middlewares/Third_Party/FatFs/src/ff_gen_drv.c 0x00000000   Number         0  ff_gen_drv.o ABSOLUTE
    ../Middlewares/Third_Party/FatFs/src/option/cc936.c 0x00000000   Number         0  cc936.o ABSOLUTE
    ../Middlewares/Third_Party/FatFs/src/option/syscall.c 0x00000000   Number         0  syscall.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stdlib/abort.c          0x00000000   Number         0  abort.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  frnd.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/floorf.c                      0x00000000   Number         0  floorf.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ..\Components\GD25QXX\gd25qxx.c          0x00000000   Number         0  gd25qxx.o ABSOLUTE
    ..\Components\OLED\oled.c                0x00000000   Number         0  oled.o ABSOLUTE
    ..\Components\ringbuffer\ringbuffer.c    0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\rtc.c                        0x00000000   Number         0  rtc.o ABSOLUTE
    ..\Core\Src\sdio.c                       0x00000000   Number         0  sdio.o ABSOLUTE
    ..\Core\Src\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_mmc.c 0x00000000   Number         0  stm32f4xx_hal_mmc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rtc.c 0x00000000   Number         0  stm32f4xx_hal_rtc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rtc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rtc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sd.c 0x00000000   Number         0  stm32f4xx_hal_sd.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_sdmmc.c 0x00000000   Number         0  stm32f4xx_ll_sdmmc.o ABSOLUTE
    ..\FATFS\App\fatfs.c                     0x00000000   Number         0  fatfs.o ABSOLUTE
    ..\FATFS\Target\bsp_driver_sd.c          0x00000000   Number         0  bsp_driver_sd.o ABSOLUTE
    ..\FATFS\Target\sd_diskio.c              0x00000000   Number         0  sd_diskio.o ABSOLUTE
    ..\Middlewares\Third_Party\FatFs\src\diskio.c 0x00000000   Number         0  diskio.o ABSOLUTE
    ..\Middlewares\Third_Party\FatFs\src\ff.c 0x00000000   Number         0  ff.o ABSOLUTE
    ..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.c 0x00000000   Number         0  ff_gen_drv.o ABSOLUTE
    ..\Middlewares\Third_Party\FatFs\src\option\cc936.c 0x00000000   Number         0  cc936.o ABSOLUTE
    ..\Middlewares\Third_Party\FatFs\src\option\syscall.c 0x00000000   Number         0  syscall.o ABSOLUTE
    ..\\Components\\GD25QXX\\gd25qxx.c       0x00000000   Number         0  gd25qxx.o ABSOLUTE
    ..\\Components\\OLED\\oled.c             0x00000000   Number         0  oled.o ABSOLUTE
    ..\\sysFunction\\APP_ADC.c               0x00000000   Number         0  app_adc.o ABSOLUTE
    ..\\sysFunction\\APP_EXTSPI.c            0x00000000   Number         0  app_extspi.o ABSOLUTE
    ..\\sysFunction\\APP_FLASH.c             0x00000000   Number         0  app_flash.o ABSOLUTE
    ..\\sysFunction\\APP_Key.c               0x00000000   Number         0  app_key.o ABSOLUTE
    ..\\sysFunction\\APP_Led.c               0x00000000   Number         0  app_led.o ABSOLUTE
    ..\\sysFunction\\APP_OLED.c              0x00000000   Number         0  app_oled.o ABSOLUTE
    ..\\sysFunction\\APP_RTC.c               0x00000000   Number         0  app_rtc.o ABSOLUTE
    ..\\sysFunction\\APP_Uart.c              0x00000000   Number         0  app_uart.o ABSOLUTE
    ..\\sysFunction\\Schedular.c             0x00000000   Number         0  schedular.o ABSOLUTE
    ..\sysFunction\APP_ADC.c                 0x00000000   Number         0  app_adc.o ABSOLUTE
    ..\sysFunction\APP_EXTSPI.c              0x00000000   Number         0  app_extspi.o ABSOLUTE
    ..\sysFunction\APP_FLASH.c               0x00000000   Number         0  app_flash.o ABSOLUTE
    ..\sysFunction\APP_Key.c                 0x00000000   Number         0  app_key.o ABSOLUTE
    ..\sysFunction\APP_Led.c                 0x00000000   Number         0  app_led.o ABSOLUTE
    ..\sysFunction\APP_OLED.c                0x00000000   Number         0  app_oled.o ABSOLUTE
    ..\sysFunction\APP_RTC.c                 0x00000000   Number         0  app_rtc.o ABSOLUTE
    ..\sysFunction\APP_Uart.c                0x00000000   Number         0  app_uart.o ABSOLUTE
    ..\sysFunction\Schedular.c               0x00000000   Number         0  schedular.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f427xx.s                    0x00000000   Number         0  startup_stm32f427xx.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f427xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080001bc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080001c0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080001c0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080001c0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001c0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c4   Section       36  startup_stm32f427xx.o(.text)
    $v0                                      0x080001c4   Number         0  startup_stm32f427xx.o(.text)
    .text                                    0x080001e8   Section        0  uldiv.o(.text)
    .text                                    0x0800024a   Section        0  memcpya.o(.text)
    .text                                    0x0800026e   Section        0  memseta.o(.text)
    .text                                    0x08000292   Section        0  strstr.o(.text)
    .text                                    0x080002b6   Section        0  strncpy.o(.text)
    .text                                    0x080002ce   Section        0  strchr.o(.text)
    .text                                    0x080002e2   Section        0  strlen.o(.text)
    .text                                    0x080002f0   Section        0  strcmp.o(.text)
    .text                                    0x0800030c   Section        0  strcpy.o(.text)
    .text                                    0x0800031e   Section        0  strncmp.o(.text)
    .text                                    0x0800033c   Section        0  strtok.o(.text)
    .text                                    0x08000380   Section        0  __0sscanf.o(.text)
    .text                                    0x080003b8   Section        0  _scanf_int.o(.text)
    .text                                    0x08000504   Section        0  scanf_fp.o(.text)
    _fp_value                                0x08000505   Thumb Code   296  scanf_fp.o(.text)
    .text                                    0x08000864   Section        0  dmul.o(.text)
    .text                                    0x08000948   Section        0  f2d.o(.text)
    .text                                    0x08000970   Section       48  cdcmple.o(.text)
    .text                                    0x080009a0   Section       48  cdrcmple.o(.text)
    .text                                    0x080009d0   Section        0  d2f.o(.text)
    .text                                    0x08000a08   Section        0  uidiv.o(.text)
    .text                                    0x08000a34   Section        0  llshl.o(.text)
    .text                                    0x08000a52   Section        0  llushr.o(.text)
    .text                                    0x08000a72   Section        0  _chval.o(.text)
    .text                                    0x08000a90   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08000a91   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000ab8   Section        0  _sgetc.o(.text)
    .text                                    0x08000af8   Section        0  strtod.o(.text)
    _local_sscanf                            0x08000af9   Thumb Code    54  strtod.o(.text)
    .text                                    0x08000b94   Section        0  fepilogue.o(.text)
    .text                                    0x08000b94   Section        0  iusefp.o(.text)
    .text                                    0x08000c02   Section        0  frnd.o(.text)
    .text                                    0x08000c3e   Section        0  depilogue.o(.text)
    .text                                    0x08000cf8   Section        0  dadd.o(.text)
    .text                                    0x08000e46   Section        0  ddiv.o(.text)
    .text                                    0x08000f24   Section        0  dfltul.o(.text)
    .text                                    0x08000f3c   Section        0  dfixul.o(.text)
    .text                                    0x08000f6c   Section       36  init.o(.text)
    .text                                    0x08000f90   Section        0  llsshr.o(.text)
    .text                                    0x08000fb4   Section        0  isspace_c.o(.text)
    .text                                    0x08000fc0   Section        0  _scanf.o(.text)
    .text                                    0x080012f0   Section        0  ctype_c.o(.text)
    i.ADC_DMAConvCplt                        0x08001318   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAConvCplt                          0x08001319   Thumb Code   110  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x08001386   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAError                             0x08001387   Thumb Code    22  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x0800139c   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_DMAHalfConvCplt                      0x0800139d   Thumb Code    10  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_Data_Storage_Task                  0x080013a8   Section        0  app_adc.o(i.ADC_Data_Storage_Task)
    i.ADC_Init                               0x08001448   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x08001449   Thumb Code   284  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.ADC_Proc                               0x08001570   Section        0  app_adc.o(i.ADC_Proc)
    i.ADC_TASK                               0x080015bc   Section        0  app_adc.o(i.ADC_TASK)
    i.ADS1220_CS_HIGH                        0x080017c0   Section        0  app_extspi.o(i.ADS1220_CS_HIGH)
    i.ADS1220_CS_LOW                         0x080017d0   Section        0  app_extspi.o(i.ADS1220_CS_LOW)
    i.ADS1220_Init_AIN0_SingleEnded          0x080017e0   Section        0  app_extspi.o(i.ADS1220_Init_AIN0_SingleEnded)
    i.ADS1220_ReadRegister                   0x08001864   Section        0  app_extspi.o(i.ADS1220_ReadRegister)
    i.ADS1220_SendCommand                    0x080018b4   Section        0  app_extspi.o(i.ADS1220_SendCommand)
    i.ADS1220_WriteRegister                  0x080018e0   Section        0  app_extspi.o(i.ADS1220_WriteRegister)
    i.BSP_SD_GetCardInfo                     0x0800191c   Section        0  bsp_driver_sd.o(i.BSP_SD_GetCardInfo)
    i.BSP_SD_GetCardState                    0x08001928   Section        0  bsp_driver_sd.o(i.BSP_SD_GetCardState)
    i.BSP_SD_Init                            0x08001940   Section        0  bsp_driver_sd.o(i.BSP_SD_Init)
    i.BSP_SD_IsDetected                      0x08001970   Section        0  bsp_driver_sd.o(i.BSP_SD_IsDetected)
    i.BSP_SD_ReadBlocks                      0x08001980   Section        0  bsp_driver_sd.o(i.BSP_SD_ReadBlocks)
    i.BSP_SD_WriteBlocks                     0x080019a0   Section        0  bsp_driver_sd.o(i.BSP_SD_WriteBlocks)
    i.BusFault_Handler                       0x080019c0   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream0_IRQHandler                0x080019c4   Section        0  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA1_Stream5_IRQHandler                0x080019d0   Section        0  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    i.DMA1_Stream7_IRQHandler                0x080019dc   Section        0  stm32f4xx_it.o(i.DMA1_Stream7_IRQHandler)
    i.DMA2_Stream0_IRQHandler                0x080019e8   Section        0  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x080019f4   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x080019f5   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08001a1c   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08001a1d   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08001a70   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08001a71   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08001a98   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x08001a9a   Section        0  main.o(i.Error_Handler)
    i.FATFS_LinkDriver                       0x08001a9e   Section        0  ff_gen_drv.o(i.FATFS_LinkDriver)
    i.FATFS_LinkDriverEx                     0x08001aa4   Section        0  ff_gen_drv.o(i.FATFS_LinkDriverEx)
    i.HAL_ADC_ConfigChannel                  0x08001ae4   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08001c48   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x08001c4a   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x08001c4c   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_Init                           0x08001c4e   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08001ca4   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x08001d5c   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_DMA_Abort                          0x08001eb4   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08001f46   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08001f6c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x0800210c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080021e0   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08002250   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08002274   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080024a8   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x080024b2   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080024bc   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2CEx_ConfigAnalogFilter           0x080024c8   Section        0  stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    i.HAL_I2CEx_ConfigDigitalFilter          0x0800250a   Section        0  stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    i.HAL_I2C_Init                           0x08002548   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Mem_Write                      0x080026d0   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x08002800   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x0800286c   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x0800287c   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080028b0   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080028f0   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002920   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x0800293c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x0800297c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_EnableOverDrive              0x080029a0   Section        0  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    i.HAL_RCCEx_PeriphCLKConfig              0x08002a10   Section        0  stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08002c64   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08002d98   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08002db8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08002dd8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08002e3c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_RTC_GetDate                        0x080031a8   Section        0  stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate)
    i.HAL_RTC_GetTime                        0x080031ec   Section        0  stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime)
    i.HAL_RTC_Init                           0x08003240   Section        0  stm32f4xx_hal_rtc.o(i.HAL_RTC_Init)
    i.HAL_RTC_MspInit                        0x080032dc   Section        0  rtc.o(i.HAL_RTC_MspInit)
    i.HAL_RTC_SetDate                        0x08003318   Section        0  stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate)
    i.HAL_RTC_SetTime                        0x080033b0   Section        0  stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime)
    i.HAL_RTC_WaitForSynchro                 0x08003470   Section        0  stm32f4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro)
    i.HAL_SD_ConfigWideBusOperation          0x080034a8   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation)
    i.HAL_SD_GetCardCSD                      0x080035c0   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCSD)
    i.HAL_SD_GetCardInfo                     0x08003758   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardInfo)
    i.HAL_SD_GetCardState                    0x0800377c   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState)
    i.HAL_SD_Init                            0x080037a8   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_Init)
    i.HAL_SD_InitCard                        0x080037e0   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_InitCard)
    i.HAL_SD_MspInit                         0x08003858   Section        0  sdio.o(i.HAL_SD_MspInit)
    i.HAL_SD_ReadBlocks                      0x080038ec   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks)
    i.HAL_SD_WriteBlocks                     0x08003acc   Section        0  stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks)
    i.HAL_SPI_Init                           0x08003c7c   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x08003d38   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SPI_Receive                        0x08003e44   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Receive)
    i.HAL_SPI_Transmit                       0x08003f98   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit)
    i.HAL_SPI_TransmitReceive                0x080040fe   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    i.HAL_SYSTICK_Config                     0x080042ee   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08004318   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x080043a8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08004404   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_ConfigClockSource              0x08004444   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08004520   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x0800456c   Section        0  app_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x080045dc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x0800464c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08004650   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080048d0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004934   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x080049e4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08004a00   Section        0  app_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08004a3c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08004a3e   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004ae0   Section        0  app_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08004b08   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x08004b0a   Section        0  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08004b0b   Thumb Code    46  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_RequestMemoryWrite                 0x08004b38   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x08004b39   Thumb Code   162  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x08004be0   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x08004be1   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x08004c38   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08004c39   Thumb Code   144  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x08004cc8   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x08004cc9   Thumb Code   188  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x08004d84   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08004d85   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.Key_Proc                               0x08004ddc   Section        0  app_key.o(i.Key_Proc)
    i.Key_Read                               0x08005028   Section        0  app_key.o(i.Key_Read)
    i.LED_SHINE                              0x08005088   Section        0  app_led.o(i.LED_SHINE)
    i.Led_Disp                               0x080050ac   Section        0  app_led.o(i.Led_Disp)
    i.Led_Proc                               0x08005128   Section        0  app_led.o(i.Led_Proc)
    i.MX_ADC1_Init                           0x08005134   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DMA_Init                            0x08005198   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_FATFS_Init                          0x08005204   Section        0  fatfs.o(i.MX_FATFS_Init)
    i.MX_GPIO_Init                           0x08005220   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x0800533c   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_RTC_Init                            0x08005398   Section        0  rtc.o(i.MX_RTC_Init)
    i.MX_SDIO_SD_Init                        0x0800541c   Section        0  sdio.o(i.MX_SDIO_SD_Init)
    i.MX_SPI2_Init                           0x0800543c   Section        0  spi.o(i.MX_SPI2_Init)
    i.MX_SPI3_Init                           0x08005484   Section        0  spi.o(i.MX_SPI3_Init)
    i.MX_TIM3_Init                           0x080054cc   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM6_Init                           0x08005534   Section        0  tim.o(i.MX_TIM6_Init)
    i.MX_USART2_UART_Init                    0x0800557c   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MemManage_Handler                      0x080055e0   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080055e2   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x080055e4   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x08005618   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Position                      0x08005648   Section        0  oled.o(i.OLED_Set_Position)
    i.OLED_ShowChar                          0x0800566c   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowStr                           0x080056f4   Section        0  oled.o(i.OLED_ShowStr)
    i.OLED_Write_cmd                         0x0800572c   Section        0  oled.o(i.OLED_Write_cmd)
    i.OLED_Write_data                        0x08005750   Section        0  oled.o(i.OLED_Write_data)
    i.Oled_Printf                            0x08005774   Section        0  app_oled.o(i.Oled_Printf)
    i.PendSV_Handler                         0x080057a2   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.RS485_Transmit                         0x080057a4   Section        0  app_uart.o(i.RS485_Transmit)
    i.RTC_Bcd2ToByte                         0x080057ec   Section        0  stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte)
    i.RTC_ByteToBcd2                         0x080057fe   Section        0  stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2)
    i.RTC_EnterInitMode                      0x08005814   Section        0  stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode)
    i.RTC_ExitInitMode                       0x0800585c   Section        0  stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode)
    i.RTC_Task                               0x08005888   Section        0  app_rtc.o(i.RTC_Task)
    i.SDIO_ConfigData                        0x080058b0   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData)
    i.SDIO_GetPowerState                     0x080058d4   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_GetPowerState)
    i.SDIO_GetResponse                       0x080058dc   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse)
    i.SDIO_Init                              0x080058e2   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_Init)
    i.SDIO_PowerState_ON                     0x08005906   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_ON)
    i.SDIO_ReadFIFO                          0x0800590e   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO)
    i.SDIO_SendCommand                       0x08005914   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand)
    i.SDIO_WriteFIFO                         0x08005934   Section        0  stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO)
    i.SDMMC_CmdAppCommand                    0x0800593e   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand)
    i.SDMMC_CmdAppOperCommand                0x08005970   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand)
    i.SDMMC_CmdBlockLength                   0x080059a4   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength)
    i.SDMMC_CmdBusWidth                      0x080059d6   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth)
    i.SDMMC_CmdGoIdleState                   0x08005a08   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState)
    i.SDMMC_CmdOperCond                      0x08005a58   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond)
    i.SDMMC_CmdReadMultiBlock                0x08005a88   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock)
    i.SDMMC_CmdReadSingleBlock               0x08005aba   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock)
    i.SDMMC_CmdSelDesel                      0x08005aec   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel)
    i.SDMMC_CmdSendCID                       0x08005b1e   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID)
    i.SDMMC_CmdSendCSD                       0x08005b4a   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD)
    i.SDMMC_CmdSendSCR                       0x08005b76   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR)
    i.SDMMC_CmdSendStatus                    0x08005ba6   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus)
    i.SDMMC_CmdSetRelAdd                     0x08005bd8   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd)
    i.SDMMC_CmdStopTransfer                  0x08005c08   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer)
    i.SDMMC_CmdWriteMultiBlock               0x08005c3c   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock)
    i.SDMMC_CmdWriteSingleBlock              0x08005c6e   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock)
    i.SDMMC_GetCmdResp1                      0x08005ca0   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1)
    i.SDMMC_GetCmdResp2                      0x08005dc0   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2)
    i.SDMMC_GetCmdResp3                      0x08005e10   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3)
    i.SDMMC_GetCmdResp6                      0x08005e54   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6)
    i.SDMMC_GetCmdResp7                      0x08005edc   Section        0  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7)
    i.SD_CheckStatus                         0x08005f34   Section        0  sd_diskio.o(i.SD_CheckStatus)
    SD_CheckStatus                           0x08005f35   Thumb Code    26  sd_diskio.o(i.SD_CheckStatus)
    i.SD_Create_New_HideData_File            0x08005f54   Section        0  app_flash.o(i.SD_Create_New_HideData_File)
    i.SD_Create_New_OverLimit_File           0x08005fb4   Section        0  app_flash.o(i.SD_Create_New_OverLimit_File)
    i.SD_Create_New_Sample_File              0x08006014   Section        0  app_flash.o(i.SD_Create_New_Sample_File)
    i.SD_FindSCR                             0x08006070   Section        0  stm32f4xx_hal_sd.o(i.SD_FindSCR)
    SD_FindSCR                               0x08006071   Thumb Code   224  stm32f4xx_hal_sd.o(i.SD_FindSCR)
    i.SD_Generate_DateTime_String            0x08006150   Section        0  app_flash.o(i.SD_Generate_DateTime_String)
    i.SD_Get_Card_Memory_KB                  0x080061c0   Section        0  app_flash.o(i.SD_Get_Card_Memory_KB)
    i.SD_Get_Power_On_Count                  0x08006204   Section        0  app_flash.o(i.SD_Get_Power_On_Count)
    i.SD_Increment_Power_On_Count            0x08006264   Section        0  app_flash.o(i.SD_Increment_Power_On_Count)
    i.SD_InitCard                            0x0800628c   Section        0  stm32f4xx_hal_sd.o(i.SD_InitCard)
    SD_InitCard                              0x0800628d   Thumb Code   238  stm32f4xx_hal_sd.o(i.SD_InitCard)
    i.SD_Init_Folders                        0x0800637c   Section        0  app_flash.o(i.SD_Init_Folders)
    i.SD_Init_Log_File                       0x080063f8   Section        0  app_flash.o(i.SD_Init_Log_File)
    i.SD_PowerON                             0x08006428   Section        0  stm32f4xx_hal_sd.o(i.SD_PowerON)
    SD_PowerON                               0x08006429   Thumb Code   174  stm32f4xx_hal_sd.o(i.SD_PowerON)
    i.SD_Read_conf_Task                      0x080064dc   Section        0  app_flash.o(i.SD_Read_conf_Task)
    i.SD_Reset_Power_On_Count                0x080064ec   Section        0  app_flash.o(i.SD_Reset_Power_On_Count)
    i.SD_Save_HideData                       0x08006514   Section        0  app_flash.o(i.SD_Save_HideData)
    i.SD_Save_OverLimit_Data                 0x08006674   Section        0  app_flash.o(i.SD_Save_OverLimit_Data)
    i.SD_Save_Sample_Data                    0x08006790   Section        0  app_flash.o(i.SD_Save_Sample_Data)
    i.SD_Test                                0x08006894   Section        0  app_flash.o(i.SD_Test)
    i.SD_Write_Log                           0x080068b4   Section        0  app_flash.o(i.SD_Write_Log)
    i.SD_Write_Log_With_Timestamp            0x08006928   Section        0  app_flash.o(i.SD_Write_Log_With_Timestamp)
    i.SD_initialize                          0x08006a0c   Section        0  sd_diskio.o(i.SD_initialize)
    i.SD_ioctl                               0x08006a2c   Section        0  sd_diskio.o(i.SD_ioctl)
    i.SD_read                                0x08006a7c   Section        0  sd_diskio.o(i.SD_read)
    i.SD_status                              0x08006aa2   Section        0  sd_diskio.o(i.SD_status)
    i.SD_write                               0x08006aa8   Section        0  sd_diskio.o(i.SD_write)
    i.SPI_EndRxTransaction                   0x08006ace   Section        0  stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction)
    SPI_EndRxTransaction                     0x08006acf   Thumb Code    92  stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction)
    i.SPI_EndRxTxTransaction                 0x08006b2c   Section        0  stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    SPI_EndRxTxTransaction                   0x08006b2d   Thumb Code    98  stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    i.SPI_WaitFlagStateUntilTimeout          0x08006b98   Section        0  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    SPI_WaitFlagStateUntilTimeout            0x08006b99   Thumb Code   182  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    i.SVC_Handler                            0x08006c54   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Schedular_Init                         0x08006c58   Section        0  schedular.o(i.Schedular_Init)
    i.Schedular_Run                          0x08006c64   Section        0  schedular.o(i.Schedular_Run)
    i.SysTick_Handler                        0x08006ca8   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08006cac   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08006d50   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x08006d60   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x08006e30   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08006e44   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08006e45   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08006e54   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08006e55   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08006e76   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08006e77   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_DMAAbortOnError                   0x08006e9a   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08006e9b   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08006ea8   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08006ea9   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08006ef2   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08006ef3   Thumb Code   134  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08006f78   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08006f79   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08006f96   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08006f97   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x08006fe4   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08006fe5   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08007000   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08007001   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080070c4   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080070c5   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x080071d0   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x08007270   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x080072a6   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x080072a7   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART2_IRQHandler                      0x08007318   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.Uart2_Proc                             0x08007324   Section        0  app_uart.o(i.Uart2_Proc)
    i.Update_Sample_Cycle                    0x0800737c   Section        0  schedular.o(i.Update_Sample_Cycle)
    i.UsageFault_Handler                     0x080073b0   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.Write_Log_With_Timestamp_Universal     0x080073b4   Section        0  app_flash.o(i.Write_Log_With_Timestamp_Universal)
    i.__0snprintf                            0x080073c8   Section        0  printfa.o(i.__0snprintf)
    i.__0vsnprintf                           0x080073fc   Section        0  printfa.o(i.__0vsnprintf)
    i.__NVIC_SetPriority                     0x08007430   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08007431   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_atof                          0x08007450   Section        0  atof.o(i.__hardfp_atof)
    i.__hardfp_floorf                        0x08007488   Section        0  floorf.o(i.__hardfp_floorf)
    i.__hardfp_roundf                        0x080074f8   Section        0  roundf.o(i.__hardfp_roundf)
    i.__read_errno                           0x08007594   Section        0  errno.o(i.__read_errno)
    i.__scatterload_copy                     0x080075a0   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080075ae   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080075b0   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x080075c0   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x080075cc   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080075cd   Thumb Code   366  printfa.o(i._fp_digits)
    i._is_digit                              0x08007750   Section        0  scanf_fp.o(i._is_digit)
    i._printf_core                           0x08007760   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08007761   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08007e14   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08007e15   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08007e38   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08007e39   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08007e66   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08007e67   Thumb Code    22  printfa.o(i._snputc)
    i.adc_dma_init                           0x08007e7c   Section        0  app_adc.o(i.adc_dma_init)
    i.check_fs                               0x08007e90   Section        0  ff.o(i.check_fs)
    check_fs                                 0x08007e91   Thumb Code   102  ff.o(i.check_fs)
    i.chk_chr                                0x08007f00   Section        0  ff.o(i.chk_chr)
    chk_chr                                  0x08007f01   Thumb Code    16  ff.o(i.chk_chr)
    i.chk_lock                               0x08007f10   Section        0  ff.o(i.chk_lock)
    chk_lock                                 0x08007f11   Thumb Code    86  ff.o(i.chk_lock)
    i.clear_lock                             0x08007f6c   Section        0  ff.o(i.clear_lock)
    clear_lock                               0x08007f6d   Thumb Code    28  ff.o(i.clear_lock)
    i.clmt_clust                             0x08007f8c   Section        0  ff.o(i.clmt_clust)
    clmt_clust                               0x08007f8d   Thumb Code    36  ff.o(i.clmt_clust)
    i.clust2sect                             0x08007fb0   Section        0  ff.o(i.clust2sect)
    clust2sect                               0x08007fb1   Thumb Code    24  ff.o(i.clust2sect)
    i.cmp_lfn                                0x08007fc8   Section        0  ff.o(i.cmp_lfn)
    cmp_lfn                                  0x08007fc9   Thumb Code   124  ff.o(i.cmp_lfn)
    i.create_chain                           0x08008048   Section        0  ff.o(i.create_chain)
    create_chain                             0x08008049   Thumb Code   164  ff.o(i.create_chain)
    i.create_name                            0x080080ec   Section        0  ff.o(i.create_name)
    create_name                              0x080080ed   Thumb Code   560  ff.o(i.create_name)
    i.datetime_to_timestamp                  0x08008330   Section        0  app_rtc.o(i.datetime_to_timestamp)
    datetime_to_timestamp                    0x08008331   Thumb Code   126  app_rtc.o(i.datetime_to_timestamp)
    i.dec_lock                               0x080083b8   Section        0  ff.o(i.dec_lock)
    dec_lock                                 0x080083b9   Thumb Code    48  ff.o(i.dec_lock)
    i.dir_find                               0x080083ec   Section        0  ff.o(i.dir_find)
    dir_find                                 0x080083ed   Thumb Code   208  ff.o(i.dir_find)
    i.dir_next                               0x080084bc   Section        0  ff.o(i.dir_next)
    dir_next                                 0x080084bd   Thumb Code   244  ff.o(i.dir_next)
    i.dir_register                           0x080085b0   Section        0  ff.o(i.dir_register)
    dir_register                             0x080085b1   Thumb Code   480  ff.o(i.dir_register)
    i.dir_sdi                                0x08008794   Section        0  ff.o(i.dir_sdi)
    dir_sdi                                  0x08008795   Thumb Code   130  ff.o(i.dir_sdi)
    i.disk_initialize                        0x08008818   Section        0  diskio.o(i.disk_initialize)
    i.disk_ioctl                             0x08008844   Section        0  diskio.o(i.disk_ioctl)
    i.disk_read                              0x08008860   Section        0  diskio.o(i.disk_read)
    i.disk_status                            0x0800887c   Section        0  diskio.o(i.disk_status)
    i.disk_write                             0x08008894   Section        0  diskio.o(i.disk_write)
    i.f_close                                0x080088b0   Section        0  ff.o(i.f_close)
    i.f_getfree                              0x080088d8   Section        0  ff.o(i.f_getfree)
    i.f_lseek                                0x080089ac   Section        0  ff.o(i.f_lseek)
    i.f_mkdir                                0x08008bc0   Section        0  ff.o(i.f_mkdir)
    i.f_mount                                0x08008d34   Section        0  ff.o(i.f_mount)
    i.f_open                                 0x08008d8c   Section        0  ff.o(i.f_open)
    i.f_read                                 0x08008fd0   Section        0  ff.o(i.f_read)
    i.f_sync                                 0x08009122   Section        0  ff.o(i.f_sync)
    i.f_write                                0x080091b0   Section        0  ff.o(i.f_write)
    i.ff_convert                             0x08009340   Section        0  cc936.o(i.ff_convert)
    i.ff_memalloc                            0x0800938c   Section        0  syscall.o(i.ff_memalloc)
    i.ff_memfree                             0x08009390   Section        0  syscall.o(i.ff_memfree)
    i.ff_wtoupper                            0x08009394   Section        0  cc936.o(i.ff_wtoupper)
    i.find_volume                            0x08009414   Section        0  ff.o(i.find_volume)
    find_volume                              0x08009415   Thumb Code   582  ff.o(i.find_volume)
    i.flash_read                             0x08009668   Section        0  app_flash.o(i.flash_read)
    i.flash_write                            0x08009690   Section        0  app_flash.o(i.flash_write)
    i.follow_path                            0x080096dc   Section        0  ff.o(i.follow_path)
    follow_path                              0x080096dd   Thumb Code   116  ff.o(i.follow_path)
    i.free                                   0x08009750   Section        0  malloc.o(i.free)
    i.gen_numname                            0x080097a0   Section        0  ff.o(i.gen_numname)
    gen_numname                              0x080097a1   Thumb Code   136  ff.o(i.gen_numname)
    i.get_fat                                0x0800982c   Section        0  ff.o(i.get_fat)
    get_fat                                  0x0800982d   Thumb Code   190  ff.o(i.get_fat)
    i.get_fattime                            0x080098ea   Section        0  fatfs.o(i.get_fattime)
    i.get_ldnumber                           0x080098ee   Section        0  ff.o(i.get_ldnumber)
    get_ldnumber                             0x080098ef   Thumb Code    60  ff.o(i.get_ldnumber)
    i.inc_lock                               0x0800992c   Section        0  ff.o(i.inc_lock)
    inc_lock                                 0x0800992d   Thumb Code   124  ff.o(i.inc_lock)
    i.is_leap_year                           0x080099ac   Section        0  app_rtc.o(i.is_leap_year)
    is_leap_year                             0x080099ad   Thumb Code    38  app_rtc.o(i.is_leap_year)
    i.ld_clust                               0x080099d2   Section        0  ff.o(i.ld_clust)
    ld_clust                                 0x080099d3   Thumb Code    38  ff.o(i.ld_clust)
    i.ld_dword                               0x080099f8   Section        0  ff.o(i.ld_dword)
    ld_dword                                 0x080099f9   Thumb Code    22  ff.o(i.ld_dword)
    i.ld_word                                0x08009a0e   Section        0  ff.o(i.ld_word)
    ld_word                                  0x08009a0f   Thumb Code    10  ff.o(i.ld_word)
    i.main                                   0x08009a18   Section        0  main.o(i.main)
    i.malloc                                 0x08009b6c   Section        0  malloc.o(i.malloc)
    i.mem_cpy                                0x08009bd8   Section        0  ff.o(i.mem_cpy)
    mem_cpy                                  0x08009bd9   Thumb Code    18  ff.o(i.mem_cpy)
    i.mem_set                                0x08009bec   Section        0  ff.o(i.mem_set)
    mem_set                                  0x08009bed   Thumb Code    14  ff.o(i.mem_set)
    i.move_window                            0x08009bfa   Section        0  ff.o(i.move_window)
    move_window                              0x08009bfb   Thumb Code    50  ff.o(i.move_window)
    i.oled_task                              0x08009c2c   Section        0  app_oled.o(i.oled_task)
    i.parse_rtc_datetime_string              0x08009d08   Section        0  app_rtc.o(i.parse_rtc_datetime_string)
    i.parse_uart_command                     0x08009de0   Section        0  app_uart.o(i.parse_uart_command)
    i.prase_rs485_command                    0x0800ad3c   Section        0  app_uart.o(i.prase_rs485_command)
    i.put_fat                                0x0800afa0   Section        0  ff.o(i.put_fat)
    put_fat                                  0x0800afa1   Thumb Code   234  ff.o(i.put_fat)
    i.read_config_file                       0x0800b08c   Section        0  app_flash.o(i.read_config_file)
    i.read_config_from_flash                 0x0800b32c   Section        0  app_uart.o(i.read_config_from_flash)
    i.remove_chain                           0x0800b404   Section        0  ff.o(i.remove_chain)
    remove_chain                             0x0800b405   Thumb Code   116  ff.o(i.remove_chain)
    i.rs485_printf                           0x0800b478   Section        0  app_uart.o(i.rs485_printf)
    i.rt_ringbuffer_data_len                 0x0800b4a6   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x0800b4d6   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x0800b54a   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x0800b57a   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x0800b5f2   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    rt_ringbuffer_status                     0x0800b5f3   Thumb Code    32  ringbuffer.o(i.rt_ringbuffer_status)
    i.rtc_to_unix_timestamp                  0x0800b612   Section        0  app_rtc.o(i.rtc_to_unix_timestamp)
    i.save_config_to_flash                   0x0800b654   Section        0  app_uart.o(i.save_config_to_flash)
    i.spi_flash_buffer_read                  0x0800b6e0   Section        0  gd25qxx.o(i.spi_flash_buffer_read)
    i.spi_flash_init                         0x0800b73c   Section        0  gd25qxx.o(i.spi_flash_init)
    i.spi_flash_page_write                   0x0800b74c   Section        0  gd25qxx.o(i.spi_flash_page_write)
    i.spi_flash_read_id                      0x0800b7b0   Section        0  gd25qxx.o(i.spi_flash_read_id)
    i.spi_flash_sector_erase                 0x0800b800   Section        0  gd25qxx.o(i.spi_flash_sector_erase)
    i.spi_flash_send_byte                    0x0800b84c   Section        0  gd25qxx.o(i.spi_flash_send_byte)
    i.spi_flash_verify_communication         0x0800b86c   Section        0  gd25qxx.o(i.spi_flash_verify_communication)
    i.spi_flash_wait_for_write_end           0x0800b890   Section        0  gd25qxx.o(i.spi_flash_wait_for_write_end)
    i.spi_flash_write_enable                 0x0800b8c4   Section        0  gd25qxx.o(i.spi_flash_write_enable)
    i.st_clust                               0x0800b8f0   Section        0  ff.o(i.st_clust)
    st_clust                                 0x0800b8f1   Thumb Code    40  ff.o(i.st_clust)
    i.st_dword                               0x0800b918   Section        0  ff.o(i.st_dword)
    st_dword                                 0x0800b919   Thumb Code    16  ff.o(i.st_dword)
    i.st_word                                0x0800b928   Section        0  ff.o(i.st_word)
    st_word                                  0x0800b929   Thumb Code     8  ff.o(i.st_word)
    i.sum_sfn                                0x0800b930   Section        0  ff.o(i.sum_sfn)
    sum_sfn                                  0x0800b931   Thumb Code    26  ff.o(i.sum_sfn)
    i.sync_fs                                0x0800b94c   Section        0  ff.o(i.sync_fs)
    sync_fs                                  0x0800b94d   Thumb Code   126  ff.o(i.sync_fs)
    i.sync_window                            0x0800b9d4   Section        0  ff.o(i.sync_window)
    sync_window                              0x0800b9d5   Thumb Code    82  ff.o(i.sync_window)
    i.system_selftest                        0x0800ba28   Section        0  app_uart.o(i.system_selftest)
    i.timestamp_to_hex                       0x0800bbd4   Section        0  app_rtc.o(i.timestamp_to_hex)
    i.validate                               0x0800bc10   Section        0  ff.o(i.validate)
    validate                                 0x0800bc11   Thumb Code    52  ff.o(i.validate)
    i.voltage_to_hex                         0x0800bc44   Section        0  app_adc.o(i.voltage_to_hex)
    .constdata                               0x0800bcac   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x0800bcac   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x0800bcb4   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800bcc4   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800bccc   Section     2712  oled.o(.constdata)
    .constdata                               0x0800c764   Section       12  app_rtc.o(.constdata)
    days_in_month                            0x0800c764   Data          12  app_rtc.o(.constdata)
    .constdata                               0x0800c770   Section       20  sd_diskio.o(.constdata)
    .constdata                               0x0800c784   Section       42  ff.o(.constdata)
    LfnOfs                                   0x0800c784   Data          13  ff.o(.constdata)
    cst                                      0x0800c792   Data          14  ff.o(.constdata)
    cst32                                    0x0800c7a0   Data          14  ff.o(.constdata)
    .constdata                               0x0800c7ae   Section    175030  cc936.o(.constdata)
    uni2oem                                  0x0800c7ae   Data       87172  cc936.o(.constdata)
    oem2uni                                  0x08021c32   Data       87172  cc936.o(.constdata)
    cvt1                                     0x080370b6   Data         498  cc936.o(.constdata)
    cvt2                                     0x080372a8   Data         188  cc936.o(.constdata)
    .constdata                               0x08037364   Section       64  ctype_c.o(.constdata)
    .data                                    0x20000000   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section       22  oled.o(.data)
    .data                                    0x20000028   Section       12  app_adc.o(.data)
    .data                                    0x20000034   Section        4  app_key.o(.data)
    .data                                    0x20000038   Section        7  app_led.o(.data)
    temp_old                                 0x20000038   Data           1  app_led.o(.data)
    .data                                    0x2000003f   Section        1  app_oled.o(.data)
    last_mode                                0x2000003f   Data           1  app_oled.o(.data)
    .data                                    0x20000040   Section        4  app_rtc.o(.data)
    .data                                    0x20000048   Section       72  app_uart.o(.data)
    TF_card_memory                           0x20000070   Data          32  app_uart.o(.data)
    .data                                    0x20000090   Section      116  schedular.o(.data)
    schedular_task                           0x20000098   Data         108  schedular.o(.data)
    .data                                    0x20000104   Section        1  sd_diskio.o(.data)
    Stat                                     0x20000104   Data           1  sd_diskio.o(.data)
    .data                                    0x20000105   Section        5  fatfs.o(.data)
    .data                                    0x2000010c   Section        8  ff.o(.data)
    Fsid                                     0x2000010c   Data           2  ff.o(.data)
    FatFs                                    0x20000110   Data           4  ff.o(.data)
    .data                                    0x20000114   Section        4  strtok.o(.data)
    state                                    0x20000114   Data           4  strtok.o(.data)
    .data                                    0x20000118   Section        4  mvars.o(.data)
    .data                                    0x2000011c   Section        4  mvars.o(.data)
    .data                                    0x20000120   Section        4  errno.o(.data)
    _errno                                   0x20000120   Data           4  errno.o(.data)
    .bss                                     0x20000124   Section      168  adc.o(.bss)
    .bss                                     0x200001cc   Section       84  i2c.o(.bss)
    .bss                                     0x20000220   Section       32  rtc.o(.bss)
    .bss                                     0x20000240   Section      132  sdio.o(.bss)
    .bss                                     0x200002c4   Section      368  spi.o(.bss)
    .bss                                     0x20000434   Section      144  tim.o(.bss)
    .bss                                     0x200004c4   Section      168  usart.o(.bss)
    .bss                                     0x2000056c   Section      148  app_adc.o(.bss)
    .bss                                     0x20000600   Section       20  app_rtc.o(.bss)
    .bss                                     0x20000614   Section      396  app_uart.o(.bss)
    .bss                                     0x200007a0   Section      128  app_uart.o(.bss)
    .bss                                     0x20000820   Section      672  app_flash.o(.bss)
    flash_log_temp_buffer                    0x200008c0   Data         256  app_flash.o(.bss)
    .bss                                     0x20000ac0   Section      564  fatfs.o(.bss)
    .bss                                     0x20000cf4   Section       32  ff.o(.bss)
    Files                                    0x20000cf4   Data          32  ff.o(.bss)
    .bss                                     0x20000d14   Section       12  ff_gen_drv.o(.bss)
    HEAP                                     0x20000d20   Section     4096  startup_stm32f427xx.o(HEAP)
    STACK                                    0x20001d20   Section     8192  startup_stm32f427xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f427xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f427xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f427xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080001bd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080001c1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080001c1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001c5   Thumb Code     8  startup_stm32f427xx.o(.text)
    ADC_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DCMI_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA2D_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    ETH_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    EXTI0_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    EXTI1_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    EXTI2_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    EXTI3_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    EXTI4_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    FLASH_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    FMC_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    FPU_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    OTG_FS_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    OTG_HS_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    PVD_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    RCC_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    SAI1_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    SDIO_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    SPI1_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    SPI2_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    SPI3_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    SPI4_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    SPI5_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    SPI6_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM2_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM3_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM4_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM5_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM7_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    UART4_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    UART5_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    UART7_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    UART8_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    USART1_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    USART3_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    USART6_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    WWDG_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f427xx.o(.text)
    __aeabi_uldivmod                         0x080001e9   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x0800024b   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800024b   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800024b   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800026f   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800026f   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800026f   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800027d   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800027d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800027d   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000281   Thumb Code    18  memseta.o(.text)
    strstr                                   0x08000293   Thumb Code    36  strstr.o(.text)
    strncpy                                  0x080002b7   Thumb Code    24  strncpy.o(.text)
    strchr                                   0x080002cf   Thumb Code    20  strchr.o(.text)
    strlen                                   0x080002e3   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x080002f1   Thumb Code    28  strcmp.o(.text)
    strcpy                                   0x0800030d   Thumb Code    18  strcpy.o(.text)
    strncmp                                  0x0800031f   Thumb Code    30  strncmp.o(.text)
    strtok                                   0x0800033d   Thumb Code    62  strtok.o(.text)
    __0sscanf                                0x08000381   Thumb Code    48  __0sscanf.o(.text)
    _scanf_int                               0x080003b9   Thumb Code   332  _scanf_int.o(.text)
    _scanf_real                              0x0800062d   Thumb Code     0  scanf_fp.o(.text)
    _scanf_really_real                       0x0800062d   Thumb Code   556  scanf_fp.o(.text)
    __aeabi_dmul                             0x08000865   Thumb Code   228  dmul.o(.text)
    __aeabi_f2d                              0x08000949   Thumb Code    38  f2d.o(.text)
    __aeabi_cdcmpeq                          0x08000971   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x08000971   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x080009a1   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_d2f                              0x080009d1   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08000a09   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000a09   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x08000a35   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000a35   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000a53   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000a53   Thumb Code     0  llushr.o(.text)
    _chval                                   0x08000a73   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08000a9d   Thumb Code    20  scanf_char.o(.text)
    _sgetc                                   0x08000ab9   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000ad7   Thumb Code    34  _sgetc.o(.text)
    __strtod_int                             0x08000b2f   Thumb Code    90  strtod.o(.text)
    __I$use$fp                               0x08000b95   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000b95   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000ba7   Thumb Code    92  fepilogue.o(.text)
    _frnd                                    0x08000c03   Thumb Code    60  frnd.o(.text)
    _double_round                            0x08000c3f   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000c5d   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x08000cf9   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000e3b   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000e41   Thumb Code     6  dadd.o(.text)
    __aeabi_ddiv                             0x08000e47   Thumb Code   222  ddiv.o(.text)
    __aeabi_ul2d                             0x08000f25   Thumb Code    24  dfltul.o(.text)
    __aeabi_d2ulz                            0x08000f3d   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x08000f6d   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000f6d   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000f91   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000f91   Thumb Code     0  llsshr.o(.text)
    isspace                                  0x08000fb5   Thumb Code    10  isspace_c.o(.text)
    __vfscanf                                0x08000fc1   Thumb Code   810  _scanf.o(.text)
    __ctype_lookup                           0x080012f1   Thumb Code    34  ctype_c.o(.text)
    ADC_Data_Storage_Task                    0x080013a9   Thumb Code   132  app_adc.o(i.ADC_Data_Storage_Task)
    ADC_Proc                                 0x08001571   Thumb Code    60  app_adc.o(i.ADC_Proc)
    ADC_TASK                                 0x080015bd   Thumb Code   396  app_adc.o(i.ADC_TASK)
    ADS1220_CS_HIGH                          0x080017c1   Thumb Code    10  app_extspi.o(i.ADS1220_CS_HIGH)
    ADS1220_CS_LOW                           0x080017d1   Thumb Code    10  app_extspi.o(i.ADS1220_CS_LOW)
    ADS1220_Init_AIN0_SingleEnded            0x080017e1   Thumb Code    54  app_extspi.o(i.ADS1220_Init_AIN0_SingleEnded)
    ADS1220_ReadRegister                     0x08001865   Thumb Code    76  app_extspi.o(i.ADS1220_ReadRegister)
    ADS1220_SendCommand                      0x080018b5   Thumb Code    38  app_extspi.o(i.ADS1220_SendCommand)
    ADS1220_WriteRegister                    0x080018e1   Thumb Code    56  app_extspi.o(i.ADS1220_WriteRegister)
    BSP_SD_GetCardInfo                       0x0800191d   Thumb Code     8  bsp_driver_sd.o(i.BSP_SD_GetCardInfo)
    BSP_SD_GetCardState                      0x08001929   Thumb Code    20  bsp_driver_sd.o(i.BSP_SD_GetCardState)
    BSP_SD_Init                              0x08001941   Thumb Code    42  bsp_driver_sd.o(i.BSP_SD_Init)
    BSP_SD_IsDetected                        0x08001971   Thumb Code    14  bsp_driver_sd.o(i.BSP_SD_IsDetected)
    BSP_SD_ReadBlocks                        0x08001981   Thumb Code    26  bsp_driver_sd.o(i.BSP_SD_ReadBlocks)
    BSP_SD_WriteBlocks                       0x080019a1   Thumb Code    26  bsp_driver_sd.o(i.BSP_SD_WriteBlocks)
    BusFault_Handler                         0x080019c1   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Stream0_IRQHandler                  0x080019c5   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x080019d1   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    DMA1_Stream7_IRQHandler                  0x080019dd   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream7_IRQHandler)
    DMA2_Stream0_IRQHandler                  0x080019e9   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    DebugMon_Handler                         0x08001a99   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x08001a9b   Thumb Code     4  main.o(i.Error_Handler)
    FATFS_LinkDriver                         0x08001a9f   Thumb Code     6  ff_gen_drv.o(i.FATFS_LinkDriver)
    FATFS_LinkDriverEx                       0x08001aa5   Thumb Code    58  ff_gen_drv.o(i.FATFS_LinkDriverEx)
    HAL_ADC_ConfigChannel                    0x08001ae5   Thumb Code   334  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08001c49   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x08001c4b   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x08001c4d   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_Init                             0x08001c4f   Thumb Code    84  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08001ca5   Thumb Code   160  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08001d5d   Thumb Code   306  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_DMA_Abort                            0x08001eb5   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001f47   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08001f6d   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x0800210d   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080021e1   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08002251   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08002275   Thumb Code   510  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080024a9   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x080024b3   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080024bd   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2CEx_ConfigAnalogFilter             0x080024c9   Thumb Code    66  stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    HAL_I2CEx_ConfigDigitalFilter            0x0800250b   Thumb Code    62  stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    HAL_I2C_Init                             0x08002549   Thumb Code   376  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Mem_Write                        0x080026d1   Thumb Code   294  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x08002801   Thumb Code    94  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x0800286d   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x0800287d   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080028b1   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080028f1   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002921   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x0800293d   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x0800297d   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_EnableOverDrive                0x080029a1   Thumb Code    98  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    HAL_RCCEx_PeriphCLKConfig                0x08002a11   Thumb Code   560  stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08002c65   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08002d99   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002db9   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002dd9   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08002e3d   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_RTC_GetDate                          0x080031a9   Thumb Code    62  stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate)
    HAL_RTC_GetTime                          0x080031ed   Thumb Code    78  stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime)
    HAL_RTC_Init                             0x08003241   Thumb Code   150  stm32f4xx_hal_rtc.o(i.HAL_RTC_Init)
    HAL_RTC_MspInit                          0x080032dd   Thumb Code    52  rtc.o(i.HAL_RTC_MspInit)
    HAL_RTC_SetDate                          0x08003319   Thumb Code   146  stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate)
    HAL_RTC_SetTime                          0x080033b1   Thumb Code   186  stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime)
    HAL_RTC_WaitForSynchro                   0x08003471   Thumb Code    50  stm32f4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro)
    HAL_SD_ConfigWideBusOperation            0x080034a9   Thumb Code   276  stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation)
    HAL_SD_GetCardCSD                        0x080035c1   Thumb Code   402  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCSD)
    HAL_SD_GetCardInfo                       0x08003759   Thumb Code    36  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardInfo)
    HAL_SD_GetCardState                      0x0800377d   Thumb Code    44  stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState)
    HAL_SD_Init                              0x080037a9   Thumb Code    54  stm32f4xx_hal_sd.o(i.HAL_SD_Init)
    HAL_SD_InitCard                          0x080037e1   Thumb Code   112  stm32f4xx_hal_sd.o(i.HAL_SD_InitCard)
    HAL_SD_MspInit                           0x08003859   Thumb Code   130  sdio.o(i.HAL_SD_MspInit)
    HAL_SD_ReadBlocks                        0x080038ed   Thumb Code   476  stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks)
    HAL_SD_WriteBlocks                       0x08003acd   Thumb Code   428  stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks)
    HAL_SPI_Init                             0x08003c7d   Thumb Code   188  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x08003d39   Thumb Code   242  spi.o(i.HAL_SPI_MspInit)
    HAL_SPI_Receive                          0x08003e45   Thumb Code   340  stm32f4xx_hal_spi.o(i.HAL_SPI_Receive)
    HAL_SPI_Transmit                         0x08003f99   Thumb Code   358  stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit)
    HAL_SPI_TransmitReceive                  0x080040ff   Thumb Code   496  stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    HAL_SYSTICK_Config                       0x080042ef   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x08004319   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080043a9   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08004405   Thumb Code    52  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x08004445   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08004521   Thumb Code    74  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x0800456d   Thumb Code    68  app_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x080045dd   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x0800464d   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08004651   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080048d1   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004935   Thumb Code   154  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x080049e5   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08004a01   Thumb Code    38  app_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08004a3d   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08004a3f   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004ae1   Thumb Code    32  app_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004b09   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    Key_Proc                                 0x08004ddd   Thumb Code   222  app_key.o(i.Key_Proc)
    Key_Read                                 0x08005029   Thumb Code    92  app_key.o(i.Key_Read)
    LED_SHINE                                0x08005089   Thumb Code    26  app_led.o(i.LED_SHINE)
    Led_Disp                                 0x080050ad   Thumb Code   116  app_led.o(i.Led_Disp)
    Led_Proc                                 0x08005129   Thumb Code     6  app_led.o(i.Led_Proc)
    MX_ADC1_Init                             0x08005135   Thumb Code    88  adc.o(i.MX_ADC1_Init)
    MX_DMA_Init                              0x08005199   Thumb Code   104  dma.o(i.MX_DMA_Init)
    MX_FATFS_Init                            0x08005205   Thumb Code    18  fatfs.o(i.MX_FATFS_Init)
    MX_GPIO_Init                             0x08005221   Thumb Code   262  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x0800533d   Thumb Code    78  i2c.o(i.MX_I2C1_Init)
    MX_RTC_Init                              0x08005399   Thumb Code   124  rtc.o(i.MX_RTC_Init)
    MX_SDIO_SD_Init                          0x0800541d   Thumb Code    24  sdio.o(i.MX_SDIO_SD_Init)
    MX_SPI2_Init                             0x0800543d   Thumb Code    62  spi.o(i.MX_SPI2_Init)
    MX_SPI3_Init                             0x08005485   Thumb Code    62  spi.o(i.MX_SPI3_Init)
    MX_TIM3_Init                             0x080054cd   Thumb Code    96  tim.o(i.MX_TIM3_Init)
    MX_TIM6_Init                             0x08005535   Thumb Code    62  tim.o(i.MX_TIM6_Init)
    MX_USART2_UART_Init                      0x0800557d   Thumb Code    84  usart.o(i.MX_USART2_UART_Init)
    MemManage_Handler                        0x080055e1   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080055e3   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x080055e5   Thumb Code    52  oled.o(i.OLED_Clear)
    OLED_Init                                0x08005619   Thumb Code    42  oled.o(i.OLED_Init)
    OLED_Set_Position                        0x08005649   Thumb Code    34  oled.o(i.OLED_Set_Position)
    OLED_ShowChar                            0x0800566d   Thumb Code   126  oled.o(i.OLED_ShowChar)
    OLED_ShowStr                             0x080056f5   Thumb Code    54  oled.o(i.OLED_ShowStr)
    OLED_Write_cmd                           0x0800572d   Thumb Code    32  oled.o(i.OLED_Write_cmd)
    OLED_Write_data                          0x08005751   Thumb Code    32  oled.o(i.OLED_Write_data)
    Oled_Printf                              0x08005775   Thumb Code    46  app_oled.o(i.Oled_Printf)
    PendSV_Handler                           0x080057a3   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    RS485_Transmit                           0x080057a5   Thumb Code    64  app_uart.o(i.RS485_Transmit)
    RTC_Bcd2ToByte                           0x080057ed   Thumb Code    18  stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte)
    RTC_ByteToBcd2                           0x080057ff   Thumb Code    22  stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2)
    RTC_EnterInitMode                        0x08005815   Thumb Code    72  stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode)
    RTC_ExitInitMode                         0x0800585d   Thumb Code    42  stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode)
    RTC_Task                                 0x08005889   Thumb Code    26  app_rtc.o(i.RTC_Task)
    SDIO_ConfigData                          0x080058b1   Thumb Code    36  stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData)
    SDIO_GetPowerState                       0x080058d5   Thumb Code     8  stm32f4xx_ll_sdmmc.o(i.SDIO_GetPowerState)
    SDIO_GetResponse                         0x080058dd   Thumb Code     6  stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse)
    SDIO_Init                                0x080058e3   Thumb Code    36  stm32f4xx_ll_sdmmc.o(i.SDIO_Init)
    SDIO_PowerState_ON                       0x08005907   Thumb Code     8  stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_ON)
    SDIO_ReadFIFO                            0x0800590f   Thumb Code     6  stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO)
    SDIO_SendCommand                         0x08005915   Thumb Code    32  stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand)
    SDIO_WriteFIFO                           0x08005935   Thumb Code    10  stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO)
    SDMMC_CmdAppCommand                      0x0800593f   Thumb Code    50  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand)
    SDMMC_CmdAppOperCommand                  0x08005971   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand)
    SDMMC_CmdBlockLength                     0x080059a5   Thumb Code    50  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength)
    SDMMC_CmdBusWidth                        0x080059d7   Thumb Code    50  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth)
    SDMMC_CmdGoIdleState                     0x08005a09   Thumb Code    74  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState)
    SDMMC_CmdOperCond                        0x08005a59   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond)
    SDMMC_CmdReadMultiBlock                  0x08005a89   Thumb Code    50  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock)
    SDMMC_CmdReadSingleBlock                 0x08005abb   Thumb Code    50  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock)
    SDMMC_CmdSelDesel                        0x08005aed   Thumb Code    50  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel)
    SDMMC_CmdSendCID                         0x08005b1f   Thumb Code    44  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID)
    SDMMC_CmdSendCSD                         0x08005b4b   Thumb Code    44  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD)
    SDMMC_CmdSendSCR                         0x08005b77   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR)
    SDMMC_CmdSendStatus                      0x08005ba7   Thumb Code    50  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus)
    SDMMC_CmdSetRelAdd                       0x08005bd9   Thumb Code    48  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd)
    SDMMC_CmdStopTransfer                    0x08005c09   Thumb Code    46  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer)
    SDMMC_CmdWriteMultiBlock                 0x08005c3d   Thumb Code    50  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock)
    SDMMC_CmdWriteSingleBlock                0x08005c6f   Thumb Code    50  stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock)
    SDMMC_GetCmdResp1                        0x08005ca1   Thumb Code   278  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1)
    SDMMC_GetCmdResp2                        0x08005dc1   Thumb Code    76  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2)
    SDMMC_GetCmdResp3                        0x08005e11   Thumb Code    62  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3)
    SDMMC_GetCmdResp6                        0x08005e55   Thumb Code   130  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6)
    SDMMC_GetCmdResp7                        0x08005edd   Thumb Code    82  stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7)
    SD_Create_New_HideData_File              0x08005f55   Thumb Code    48  app_flash.o(i.SD_Create_New_HideData_File)
    SD_Create_New_OverLimit_File             0x08005fb5   Thumb Code    48  app_flash.o(i.SD_Create_New_OverLimit_File)
    SD_Create_New_Sample_File                0x08006015   Thumb Code    48  app_flash.o(i.SD_Create_New_Sample_File)
    SD_Generate_DateTime_String              0x08006151   Thumb Code    70  app_flash.o(i.SD_Generate_DateTime_String)
    SD_Get_Card_Memory_KB                    0x080061c1   Thumb Code    60  app_flash.o(i.SD_Get_Card_Memory_KB)
    SD_Get_Power_On_Count                    0x08006205   Thumb Code    88  app_flash.o(i.SD_Get_Power_On_Count)
    SD_Increment_Power_On_Count              0x08006265   Thumb Code    32  app_flash.o(i.SD_Increment_Power_On_Count)
    SD_Init_Folders                          0x0800637d   Thumb Code    76  app_flash.o(i.SD_Init_Folders)
    SD_Init_Log_File                         0x080063f9   Thumb Code    24  app_flash.o(i.SD_Init_Log_File)
    SD_Read_conf_Task                        0x080064dd   Thumb Code     6  app_flash.o(i.SD_Read_conf_Task)
    SD_Reset_Power_On_Count                  0x080064ed   Thumb Code    30  app_flash.o(i.SD_Reset_Power_On_Count)
    SD_Save_HideData                         0x08006515   Thumb Code   268  app_flash.o(i.SD_Save_HideData)
    SD_Save_OverLimit_Data                   0x08006675   Thumb Code   206  app_flash.o(i.SD_Save_OverLimit_Data)
    SD_Save_Sample_Data                      0x08006791   Thumb Code   196  app_flash.o(i.SD_Save_Sample_Data)
    SD_Test                                  0x08006895   Thumb Code    22  app_flash.o(i.SD_Test)
    SD_Write_Log                             0x080068b5   Thumb Code   102  app_flash.o(i.SD_Write_Log)
    SD_Write_Log_With_Timestamp              0x08006929   Thumb Code   166  app_flash.o(i.SD_Write_Log_With_Timestamp)
    SD_initialize                            0x08006a0d   Thumb Code    28  sd_diskio.o(i.SD_initialize)
    SD_ioctl                                 0x08006a2d   Thumb Code    76  sd_diskio.o(i.SD_ioctl)
    SD_read                                  0x08006a7d   Thumb Code    38  sd_diskio.o(i.SD_read)
    SD_status                                0x08006aa3   Thumb Code     4  sd_diskio.o(i.SD_status)
    SD_write                                 0x08006aa9   Thumb Code    38  sd_diskio.o(i.SD_write)
    SVC_Handler                              0x08006c55   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Schedular_Init                           0x08006c59   Thumb Code     8  schedular.o(i.Schedular_Init)
    Schedular_Run                            0x08006c65   Thumb Code    62  schedular.o(i.Schedular_Run)
    SysTick_Handler                          0x08006ca9   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08006cad   Thumb Code   154  main.o(i.SystemClock_Config)
    SystemInit                               0x08006d51   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x08006d61   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x08006e31   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    UART_Start_Receive_DMA                   0x080071d1   Thumb Code   146  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x08007271   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART2_IRQHandler                        0x08007319   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    Uart2_Proc                               0x08007325   Thumb Code    72  app_uart.o(i.Uart2_Proc)
    Update_Sample_Cycle                      0x0800737d   Thumb Code    48  schedular.o(i.Update_Sample_Cycle)
    UsageFault_Handler                       0x080073b1   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    Write_Log_With_Timestamp_Universal       0x080073b5   Thumb Code    16  app_flash.o(i.Write_Log_With_Timestamp_Universal)
    __0snprintf                              0x080073c9   Thumb Code    48  printfa.o(i.__0snprintf)
    __1snprintf                              0x080073c9   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x080073c9   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x080073c9   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x080073c9   Thumb Code     0  printfa.o(i.__0snprintf)
    __0vsnprintf                             0x080073fd   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x080073fd   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x080073fd   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x080073fd   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x080073fd   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __hardfp_atof                            0x08007451   Thumb Code    44  atof.o(i.__hardfp_atof)
    __hardfp_floorf                          0x08007489   Thumb Code    98  floorf.o(i.__hardfp_floorf)
    __hardfp_roundf                          0x080074f9   Thumb Code   154  roundf.o(i.__hardfp_roundf)
    __read_errno                             0x08007595   Thumb Code     6  errno.o(i.__read_errno)
    __scatterload_copy                       0x080075a1   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080075af   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080075b1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x080075c1   Thumb Code     6  errno.o(i.__set_errno)
    _is_digit                                0x08007751   Thumb Code    14  scanf_fp.o(i._is_digit)
    adc_dma_init                             0x08007e7d   Thumb Code    10  app_adc.o(i.adc_dma_init)
    disk_initialize                          0x08008819   Thumb Code    38  diskio.o(i.disk_initialize)
    disk_ioctl                               0x08008845   Thumb Code    24  diskio.o(i.disk_ioctl)
    disk_read                                0x08008861   Thumb Code    24  diskio.o(i.disk_read)
    disk_status                              0x0800887d   Thumb Code    18  diskio.o(i.disk_status)
    disk_write                               0x08008895   Thumb Code    24  diskio.o(i.disk_write)
    f_close                                  0x080088b1   Thumb Code    40  ff.o(i.f_close)
    f_getfree                                0x080088d9   Thumb Code   212  ff.o(i.f_getfree)
    f_lseek                                  0x080089ad   Thumb Code   530  ff.o(i.f_lseek)
    f_mkdir                                  0x08008bc1   Thumb Code   370  ff.o(i.f_mkdir)
    f_mount                                  0x08008d35   Thumb Code    84  ff.o(i.f_mount)
    f_open                                   0x08008d8d   Thumb Code   574  ff.o(i.f_open)
    f_read                                   0x08008fd1   Thumb Code   338  ff.o(i.f_read)
    f_sync                                   0x08009123   Thumb Code   142  ff.o(i.f_sync)
    f_write                                  0x080091b1   Thumb Code   398  ff.o(i.f_write)
    ff_convert                               0x08009341   Thumb Code    68  cc936.o(i.ff_convert)
    ff_memalloc                              0x0800938d   Thumb Code     4  syscall.o(i.ff_memalloc)
    ff_memfree                               0x08009391   Thumb Code     4  syscall.o(i.ff_memfree)
    ff_wtoupper                              0x08009395   Thumb Code   120  cc936.o(i.ff_wtoupper)
    flash_read                               0x08009669   Thumb Code    36  app_flash.o(i.flash_read)
    flash_write                              0x08009691   Thumb Code    76  app_flash.o(i.flash_write)
    free                                     0x08009751   Thumb Code    76  malloc.o(i.free)
    get_fattime                              0x080098eb   Thumb Code     4  fatfs.o(i.get_fattime)
    main                                     0x08009a19   Thumb Code   220  main.o(i.main)
    malloc                                   0x08009b6d   Thumb Code    92  malloc.o(i.malloc)
    oled_task                                0x08009c2d   Thumb Code   126  app_oled.o(i.oled_task)
    parse_rtc_datetime_string                0x08009d09   Thumb Code   162  app_rtc.o(i.parse_rtc_datetime_string)
    parse_uart_command                       0x08009de1   Thumb Code  3748  app_uart.o(i.parse_uart_command)
    prase_rs485_command                      0x0800ad3d   Thumb Code   240  app_uart.o(i.prase_rs485_command)
    read_config_file                         0x0800b08d   Thumb Code   464  app_flash.o(i.read_config_file)
    read_config_from_flash                   0x0800b32d   Thumb Code   144  app_uart.o(i.read_config_from_flash)
    rs485_printf                             0x0800b479   Thumb Code    46  app_uart.o(i.rs485_printf)
    rt_ringbuffer_data_len                   0x0800b4a7   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x0800b4d7   Thumb Code   116  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x0800b54b   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x0800b57b   Thumb Code   120  ringbuffer.o(i.rt_ringbuffer_put)
    rtc_to_unix_timestamp                    0x0800b613   Thumb Code    64  app_rtc.o(i.rtc_to_unix_timestamp)
    save_config_to_flash                     0x0800b655   Thumb Code    66  app_uart.o(i.save_config_to_flash)
    spi_flash_buffer_read                    0x0800b6e1   Thumb Code    88  gd25qxx.o(i.spi_flash_buffer_read)
    spi_flash_init                           0x0800b73d   Thumb Code    10  gd25qxx.o(i.spi_flash_init)
    spi_flash_page_write                     0x0800b74d   Thumb Code    94  gd25qxx.o(i.spi_flash_page_write)
    spi_flash_read_id                        0x0800b7b1   Thumb Code    74  gd25qxx.o(i.spi_flash_read_id)
    spi_flash_sector_erase                   0x0800b801   Thumb Code    70  gd25qxx.o(i.spi_flash_sector_erase)
    spi_flash_send_byte                      0x0800b84d   Thumb Code    28  gd25qxx.o(i.spi_flash_send_byte)
    spi_flash_verify_communication           0x0800b86d   Thumb Code    34  gd25qxx.o(i.spi_flash_verify_communication)
    spi_flash_wait_for_write_end             0x0800b891   Thumb Code    48  gd25qxx.o(i.spi_flash_wait_for_write_end)
    spi_flash_write_enable                   0x0800b8c5   Thumb Code    38  gd25qxx.o(i.spi_flash_write_enable)
    system_selftest                          0x0800ba29   Thumb Code   174  app_uart.o(i.system_selftest)
    timestamp_to_hex                         0x0800bbd5   Thumb Code    50  app_rtc.o(i.timestamp_to_hex)
    voltage_to_hex                           0x0800bc45   Thumb Code    92  app_adc.o(i.voltage_to_hex)
    AHBPrescTable                            0x0800bcb4   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x0800bcc4   Data           8  system_stm32f4xx.o(.constdata)
    F6X8                                     0x0800bccc   Data         552  oled.o(.constdata)
    F8X16                                    0x0800bef4   Data        1520  oled.o(.constdata)
    Hzk                                      0x0800c4e4   Data         128  oled.o(.constdata)
    Hzb                                      0x0800c564   Data         512  oled.o(.constdata)
    SD_Driver                                0x0800c770   Data          20  sd_diskio.o(.constdata)
    __ctype_categories                       0x08037364   Data          64  ctype_c.o(.constdata)
    Region$$Table$$Base                      0x080373a4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080373c4   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    initcmd1                                 0x20000010   Data          22  oled.o(.data)
    adc_val                                  0x20000028   Data           4  app_adc.o(.data)
    voltage                                  0x2000002c   Data           4  app_adc.o(.data)
    output_unix_timestamp                    0x20000030   Data           4  app_adc.o(.data)
    Key_Val                                  0x20000034   Data           1  app_key.o(.data)
    Key_Old                                  0x20000035   Data           1  app_key.o(.data)
    Key_Down                                 0x20000036   Data           1  app_key.o(.data)
    Key_Up                                   0x20000037   Data           1  app_key.o(.data)
    ucled                                    0x20000039   Data           6  app_led.o(.data)
    Date                                     0x20000040   Data           4  app_rtc.o(.data)
    rtc_config_step                          0x20000048   Data           1  app_uart.o(.data)
    ratio_config_step                        0x20000049   Data           1  app_uart.o(.data)
    limit_config_step                        0x2000004a   Data           1  app_uart.o(.data)
    hide_config_step                         0x2000004b   Data           1  app_uart.o(.data)
    Led_ADC_Mode                             0x2000004c   Data           1  app_uart.o(.data)
    uart2_rx_index                           0x2000004e   Data           2  app_uart.o(.data)
    uart2_rx_ticks                           0x20000050   Data           4  app_uart.o(.data)
    Ratio                                    0x20000058   Data           8  app_uart.o(.data)
    limit                                    0x20000060   Data           8  app_uart.o(.data)
    Ratio_Old                                0x20000068   Data           8  app_uart.o(.data)
    task_num                                 0x20000090   Data           1  schedular.o(.data)
    sample_cycle                             0x20000094   Data           4  schedular.o(.data)
    retSD                                    0x20000105   Data           1  fatfs.o(.data)
    SDPath                                   0x20000106   Data           4  fatfs.o(.data)
    __microlib_freelist                      0x20000118   Data           4  mvars.o(.data)
    __microlib_freelist_initialised          0x2000011c   Data           4  mvars.o(.data)
    hadc1                                    0x20000124   Data          72  adc.o(.bss)
    hdma_adc1                                0x2000016c   Data          96  adc.o(.bss)
    hi2c1                                    0x200001cc   Data          84  i2c.o(.bss)
    hrtc                                     0x20000220   Data          32  rtc.o(.bss)
    hsd                                      0x20000240   Data         132  sdio.o(.bss)
    hspi2                                    0x200002c4   Data          88  spi.o(.bss)
    hspi3                                    0x2000031c   Data          88  spi.o(.bss)
    hdma_spi3_rx                             0x20000374   Data          96  spi.o(.bss)
    hdma_spi3_tx                             0x200003d4   Data          96  spi.o(.bss)
    htim3                                    0x20000434   Data          72  tim.o(.bss)
    htim6                                    0x2000047c   Data          72  tim.o(.bss)
    huart2                                   0x200004c4   Data          72  usart.o(.bss)
    hdma_usart2_rx                           0x2000050c   Data          96  usart.o(.bss)
    voltage_hex                              0x2000056c   Data           9  app_adc.o(.bss)
    timestamp_hex                            0x20000575   Data           9  app_adc.o(.bss)
    adc_dma_buffer                           0x20000580   Data         128  app_adc.o(.bss)
    Time                                     0x20000600   Data          20  app_rtc.o(.bss)
    uart2_rx_buffer                          0x20000614   Data         128  app_uart.o(.bss)
    uart2_rx_dma_buffer                      0x20000694   Data         128  app_uart.o(.bss)
    uart2_dma_buffer                         0x20000714   Data         128  app_uart.o(.bss)
    uart2_ringbuffer                         0x20000794   Data          12  app_uart.o(.bss)
    uart2_ringbuffer_pool                    0x200007a0   Data         128  app_uart.o(.bss)
    file_manager                             0x20000820   Data         140  app_flash.o(.bss)
    flash_log_header                         0x200008ac   Data          20  app_flash.o(.bss)
    read_buffer                              0x200009c0   Data         256  app_flash.o(.bss)
    SDFatFS                                  0x20000ac0   Data         564  fatfs.o(.bss)
    disk                                     0x20000d14   Data          12  ff_gen_drv.o(.bss)
    __heap_base                              0x20000d20   Data           0  startup_stm32f427xx.o(HEAP)
    __heap_limit                             0x20001d20   Data           0  startup_stm32f427xx.o(HEAP)
    __initial_sp                             0x20003d20   Data           0  startup_stm32f427xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000374e8, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000373c4, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_stm32f427xx.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         7136  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         7476    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         7479    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         7481    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         7483    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         7484    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         7491    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         7486    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         7488    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO         7477    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c4   0x080001c4   0x00000024   Code   RO            4    .text               startup_stm32f427xx.o
    0x080001e8   0x080001e8   0x00000062   Code   RO         7139    .text               mc_w.l(uldiv.o)
    0x0800024a   0x0800024a   0x00000024   Code   RO         7143    .text               mc_w.l(memcpya.o)
    0x0800026e   0x0800026e   0x00000024   Code   RO         7145    .text               mc_w.l(memseta.o)
    0x08000292   0x08000292   0x00000024   Code   RO         7147    .text               mc_w.l(strstr.o)
    0x080002b6   0x080002b6   0x00000018   Code   RO         7149    .text               mc_w.l(strncpy.o)
    0x080002ce   0x080002ce   0x00000014   Code   RO         7151    .text               mc_w.l(strchr.o)
    0x080002e2   0x080002e2   0x0000000e   Code   RO         7153    .text               mc_w.l(strlen.o)
    0x080002f0   0x080002f0   0x0000001c   Code   RO         7155    .text               mc_w.l(strcmp.o)
    0x0800030c   0x0800030c   0x00000012   Code   RO         7157    .text               mc_w.l(strcpy.o)
    0x0800031e   0x0800031e   0x0000001e   Code   RO         7159    .text               mc_w.l(strncmp.o)
    0x0800033c   0x0800033c   0x00000044   Code   RO         7161    .text               mc_w.l(strtok.o)
    0x08000380   0x08000380   0x00000038   Code   RO         7458    .text               mc_w.l(__0sscanf.o)
    0x080003b8   0x080003b8   0x0000014c   Code   RO         7460    .text               mc_w.l(_scanf_int.o)
    0x08000504   0x08000504   0x00000360   Code   RO         7462    .text               mc_w.l(scanf_fp.o)
    0x08000864   0x08000864   0x000000e4   Code   RO         7466    .text               mf_w.l(dmul.o)
    0x08000948   0x08000948   0x00000026   Code   RO         7468    .text               mf_w.l(f2d.o)
    0x0800096e   0x0800096e   0x00000002   PAD
    0x08000970   0x08000970   0x00000030   Code   RO         7470    .text               mf_w.l(cdcmple.o)
    0x080009a0   0x080009a0   0x00000030   Code   RO         7472    .text               mf_w.l(cdrcmple.o)
    0x080009d0   0x080009d0   0x00000038   Code   RO         7474    .text               mf_w.l(d2f.o)
    0x08000a08   0x08000a08   0x0000002c   Code   RO         7495    .text               mc_w.l(uidiv.o)
    0x08000a34   0x08000a34   0x0000001e   Code   RO         7497    .text               mc_w.l(llshl.o)
    0x08000a52   0x08000a52   0x00000020   Code   RO         7499    .text               mc_w.l(llushr.o)
    0x08000a72   0x08000a72   0x0000001c   Code   RO         7510    .text               mc_w.l(_chval.o)
    0x08000a8e   0x08000a8e   0x00000002   PAD
    0x08000a90   0x08000a90   0x00000028   Code   RO         7512    .text               mc_w.l(scanf_char.o)
    0x08000ab8   0x08000ab8   0x00000040   Code   RO         7514    .text               mc_w.l(_sgetc.o)
    0x08000af8   0x08000af8   0x0000009c   Code   RO         7516    .text               mc_w.l(strtod.o)
    0x08000b94   0x08000b94   0x00000000   Code   RO         7518    .text               mc_w.l(iusefp.o)
    0x08000b94   0x08000b94   0x0000006e   Code   RO         7519    .text               mf_w.l(fepilogue.o)
    0x08000c02   0x08000c02   0x0000003c   Code   RO         7521    .text               mf_w.l(frnd.o)
    0x08000c3e   0x08000c3e   0x000000ba   Code   RO         7523    .text               mf_w.l(depilogue.o)
    0x08000cf8   0x08000cf8   0x0000014e   Code   RO         7525    .text               mf_w.l(dadd.o)
    0x08000e46   0x08000e46   0x000000de   Code   RO         7527    .text               mf_w.l(ddiv.o)
    0x08000f24   0x08000f24   0x00000018   Code   RO         7529    .text               mf_w.l(dfltul.o)
    0x08000f3c   0x08000f3c   0x00000030   Code   RO         7531    .text               mf_w.l(dfixul.o)
    0x08000f6c   0x08000f6c   0x00000024   Code   RO         7533    .text               mc_w.l(init.o)
    0x08000f90   0x08000f90   0x00000024   Code   RO         7536    .text               mc_w.l(llsshr.o)
    0x08000fb4   0x08000fb4   0x0000000a   Code   RO         7538    .text               mc_w.l(isspace_c.o)
    0x08000fbe   0x08000fbe   0x00000002   PAD
    0x08000fc0   0x08000fc0   0x00000330   Code   RO         7540    .text               mc_w.l(_scanf.o)
    0x080012f0   0x080012f0   0x00000028   Code   RO         7543    .text               mc_w.l(ctype_c.o)
    0x08001318   0x08001318   0x0000006e   Code   RO          804    i.ADC_DMAConvCplt   stm32f4xx_hal_adc.o
    0x08001386   0x08001386   0x00000016   Code   RO          805    i.ADC_DMAError      stm32f4xx_hal_adc.o
    0x0800139c   0x0800139c   0x0000000a   Code   RO          806    i.ADC_DMAHalfConvCplt  stm32f4xx_hal_adc.o
    0x080013a6   0x080013a6   0x00000002   PAD
    0x080013a8   0x080013a8   0x000000a0   Code   RO         5714    i.ADC_Data_Storage_Task  app_adc.o
    0x08001448   0x08001448   0x00000128   Code   RO          807    i.ADC_Init          stm32f4xx_hal_adc.o
    0x08001570   0x08001570   0x0000004c   Code   RO         5715    i.ADC_Proc          app_adc.o
    0x080015bc   0x080015bc   0x00000204   Code   RO         5716    i.ADC_TASK          app_adc.o
    0x080017c0   0x080017c0   0x00000010   Code   RO         6319    i.ADS1220_CS_HIGH   app_extspi.o
    0x080017d0   0x080017d0   0x00000010   Code   RO         6320    i.ADS1220_CS_LOW    app_extspi.o
    0x080017e0   0x080017e0   0x00000084   Code   RO         6321    i.ADS1220_Init_AIN0_SingleEnded  app_extspi.o
    0x08001864   0x08001864   0x00000050   Code   RO         6322    i.ADS1220_ReadRegister  app_extspi.o
    0x080018b4   0x080018b4   0x0000002c   Code   RO         6323    i.ADS1220_SendCommand  app_extspi.o
    0x080018e0   0x080018e0   0x0000003c   Code   RO         6325    i.ADS1220_WriteRegister  app_extspi.o
    0x0800191c   0x0800191c   0x0000000c   Code   RO         6380    i.BSP_SD_GetCardInfo  bsp_driver_sd.o
    0x08001928   0x08001928   0x00000018   Code   RO         6381    i.BSP_SD_GetCardState  bsp_driver_sd.o
    0x08001940   0x08001940   0x00000030   Code   RO         6383    i.BSP_SD_Init       bsp_driver_sd.o
    0x08001970   0x08001970   0x0000000e   Code   RO         6384    i.BSP_SD_IsDetected  bsp_driver_sd.o
    0x0800197e   0x0800197e   0x00000002   PAD
    0x08001980   0x08001980   0x00000020   Code   RO         6385    i.BSP_SD_ReadBlocks  bsp_driver_sd.o
    0x080019a0   0x080019a0   0x00000020   Code   RO         6388    i.BSP_SD_WriteBlocks  bsp_driver_sd.o
    0x080019c0   0x080019c0   0x00000002   Code   RO          671    i.BusFault_Handler  stm32f4xx_it.o
    0x080019c2   0x080019c2   0x00000002   PAD
    0x080019c4   0x080019c4   0x0000000c   Code   RO          672    i.DMA1_Stream0_IRQHandler  stm32f4xx_it.o
    0x080019d0   0x080019d0   0x0000000c   Code   RO          673    i.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x080019dc   0x080019dc   0x0000000c   Code   RO          674    i.DMA1_Stream7_IRQHandler  stm32f4xx_it.o
    0x080019e8   0x080019e8   0x0000000c   Code   RO          675    i.DMA2_Stream0_IRQHandler  stm32f4xx_it.o
    0x080019f4   0x080019f4   0x00000028   Code   RO         1593    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08001a1c   0x08001a1c   0x00000054   Code   RO         1594    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08001a70   0x08001a70   0x00000028   Code   RO         1595    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08001a98   0x08001a98   0x00000002   Code   RO          676    i.DebugMon_Handler  stm32f4xx_it.o
    0x08001a9a   0x08001a9a   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08001a9e   0x08001a9e   0x00000006   Code   RO         7005    i.FATFS_LinkDriver  ff_gen_drv.o
    0x08001aa4   0x08001aa4   0x00000040   Code   RO         7006    i.FATFS_LinkDriverEx  ff_gen_drv.o
    0x08001ae4   0x08001ae4   0x00000164   Code   RO          809    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x08001c48   0x08001c48   0x00000002   Code   RO          810    i.HAL_ADC_ConvCpltCallback  stm32f4xx_hal_adc.o
    0x08001c4a   0x08001c4a   0x00000002   Code   RO          811    i.HAL_ADC_ConvHalfCpltCallback  stm32f4xx_hal_adc.o
    0x08001c4c   0x08001c4c   0x00000002   Code   RO          813    i.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x08001c4e   0x08001c4e   0x00000054   Code   RO          818    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x08001ca2   0x08001ca2   0x00000002   PAD
    0x08001ca4   0x08001ca4   0x000000b8   Code   RO          340    i.HAL_ADC_MspInit   adc.o
    0x08001d5c   0x08001d5c   0x00000158   Code   RO          825    i.HAL_ADC_Start_DMA  stm32f4xx_hal_adc.o
    0x08001eb4   0x08001eb4   0x00000092   Code   RO         1596    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08001f46   0x08001f46   0x00000024   Code   RO         1597    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08001f6a   0x08001f6a   0x00000002   PAD
    0x08001f6c   0x08001f6c   0x000001a0   Code   RO         1601    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x0800210c   0x0800210c   0x000000d4   Code   RO         1602    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x080021e0   0x080021e0   0x0000006e   Code   RO         1606    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x0800224e   0x0800224e   0x00000002   PAD
    0x08002250   0x08002250   0x00000024   Code   RO         2051    i.HAL_Delay         stm32f4xx_hal.o
    0x08002274   0x08002274   0x00000234   Code   RO         1489    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x080024a8   0x080024a8   0x0000000a   Code   RO         1491    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x080024b2   0x080024b2   0x0000000a   Code   RO         1493    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x080024bc   0x080024bc   0x0000000c   Code   RO         2059    i.HAL_GetTick       stm32f4xx_hal.o
    0x080024c8   0x080024c8   0x00000042   Code   RO         2751    i.HAL_I2CEx_ConfigAnalogFilter  stm32f4xx_hal_i2c_ex.o
    0x0800250a   0x0800250a   0x0000003e   Code   RO         2752    i.HAL_I2CEx_ConfigDigitalFilter  stm32f4xx_hal_i2c_ex.o
    0x08002548   0x08002548   0x00000188   Code   RO         2325    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x080026d0   0x080026d0   0x00000130   Code   RO         2346    i.HAL_I2C_Mem_Write  stm32f4xx_hal_i2c.o
    0x08002800   0x08002800   0x0000006c   Code   RO          406    i.HAL_I2C_MspInit   i2c.o
    0x0800286c   0x0800286c   0x00000010   Code   RO         2065    i.HAL_IncTick       stm32f4xx_hal.o
    0x0800287c   0x0800287c   0x00000034   Code   RO         2066    i.HAL_Init          stm32f4xx_hal.o
    0x080028b0   0x080028b0   0x00000040   Code   RO         2067    i.HAL_InitTick      stm32f4xx_hal.o
    0x080028f0   0x080028f0   0x00000030   Code   RO          780    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08002920   0x08002920   0x0000001a   Code   RO         1899    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x0800293a   0x0800293a   0x00000002   PAD
    0x0800293c   0x0800293c   0x00000040   Code   RO         1905    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x0800297c   0x0800297c   0x00000024   Code   RO         1906    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080029a0   0x080029a0   0x00000070   Code   RO         1825    i.HAL_PWREx_EnableOverDrive  stm32f4xx_hal_pwr_ex.o
    0x08002a10   0x08002a10   0x00000254   Code   RO         1190    i.HAL_RCCEx_PeriphCLKConfig  stm32f4xx_hal_rcc_ex.o
    0x08002c64   0x08002c64   0x00000134   Code   RO         1083    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08002d98   0x08002d98   0x00000020   Code   RO         1090    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08002db8   0x08002db8   0x00000020   Code   RO         1091    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08002dd8   0x08002dd8   0x00000064   Code   RO         1092    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08002e3c   0x08002e3c   0x0000036c   Code   RO         1095    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080031a8   0x080031a8   0x00000044   Code   RO         2791    i.HAL_RTC_GetDate   stm32f4xx_hal_rtc.o
    0x080031ec   0x080031ec   0x00000054   Code   RO         2793    i.HAL_RTC_GetTime   stm32f4xx_hal_rtc.o
    0x08003240   0x08003240   0x0000009c   Code   RO         2794    i.HAL_RTC_Init      stm32f4xx_hal_rtc.o
    0x080032dc   0x080032dc   0x0000003c   Code   RO          448    i.HAL_RTC_MspInit   rtc.o
    0x08003318   0x08003318   0x00000098   Code   RO         2800    i.HAL_RTC_SetDate   stm32f4xx_hal_rtc.o
    0x080033b0   0x080033b0   0x000000c0   Code   RO         2801    i.HAL_RTC_SetTime   stm32f4xx_hal_rtc.o
    0x08003470   0x08003470   0x00000038   Code   RO         2802    i.HAL_RTC_WaitForSynchro  stm32f4xx_hal_rtc.o
    0x080034a8   0x080034a8   0x00000118   Code   RO         3470    i.HAL_SD_ConfigWideBusOperation  stm32f4xx_hal_sd.o
    0x080035c0   0x080035c0   0x00000198   Code   RO         3475    i.HAL_SD_GetCardCSD  stm32f4xx_hal_sd.o
    0x08003758   0x08003758   0x00000024   Code   RO         3476    i.HAL_SD_GetCardInfo  stm32f4xx_hal_sd.o
    0x0800377c   0x0800377c   0x0000002c   Code   RO         3477    i.HAL_SD_GetCardState  stm32f4xx_hal_sd.o
    0x080037a8   0x080037a8   0x00000036   Code   RO         3482    i.HAL_SD_Init       stm32f4xx_hal_sd.o
    0x080037de   0x080037de   0x00000002   PAD
    0x080037e0   0x080037e0   0x00000078   Code   RO         3483    i.HAL_SD_InitCard   stm32f4xx_hal_sd.o
    0x08003858   0x08003858   0x00000094   Code   RO          491    i.HAL_SD_MspInit    sdio.o
    0x080038ec   0x080038ec   0x000001e0   Code   RO         3486    i.HAL_SD_ReadBlocks  stm32f4xx_hal_sd.o
    0x08003acc   0x08003acc   0x000001b0   Code   RO         3491    i.HAL_SD_WriteBlocks  stm32f4xx_hal_sd.o
    0x08003c7c   0x08003c7c   0x000000bc   Code   RO         3721    i.HAL_SPI_Init      stm32f4xx_hal_spi.o
    0x08003d38   0x08003d38   0x0000010c   Code   RO          533    i.HAL_SPI_MspInit   spi.o
    0x08003e44   0x08003e44   0x00000154   Code   RO         3724    i.HAL_SPI_Receive   stm32f4xx_hal_spi.o
    0x08003f98   0x08003f98   0x00000166   Code   RO         3729    i.HAL_SPI_Transmit  stm32f4xx_hal_spi.o
    0x080040fe   0x080040fe   0x000001f0   Code   RO         3730    i.HAL_SPI_TransmitReceive  stm32f4xx_hal_spi.o
    0x080042ee   0x080042ee   0x00000028   Code   RO         1910    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08004316   0x08004316   0x00000002   PAD
    0x08004318   0x08004318   0x00000090   Code   RO         4757    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x080043a8   0x080043a8   0x0000005a   Code   RO         4034    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08004402   0x08004402   0x00000002   PAD
    0x08004404   0x08004404   0x00000040   Code   RO          581    i.HAL_TIM_Base_MspInit  tim.o
    0x08004444   0x08004444   0x000000dc   Code   RO         4043    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08004520   0x08004520   0x0000004a   Code   RO         5015    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x0800456a   0x0800456a   0x00000002   PAD
    0x0800456c   0x0800456c   0x00000070   Code   RO         5983    i.HAL_UARTEx_RxEventCallback  app_uart.o
    0x080045dc   0x080045dc   0x00000070   Code   RO         5029    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x0800464c   0x0800464c   0x00000002   Code   RO         5031    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x0800464e   0x0800464e   0x00000002   PAD
    0x08004650   0x08004650   0x00000280   Code   RO         5034    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080048d0   0x080048d0   0x00000064   Code   RO         5035    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08004934   0x08004934   0x000000b0   Code   RO          629    i.HAL_UART_MspInit  usart.o
    0x080049e4   0x080049e4   0x0000001c   Code   RO         5040    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08004a00   0x08004a00   0x0000003c   Code   RO         5984    i.HAL_UART_RxCpltCallback  app_uart.o
    0x08004a3c   0x08004a3c   0x00000002   Code   RO         5042    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08004a3e   0x08004a3e   0x000000a0   Code   RO         5043    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08004ade   0x08004ade   0x00000002   PAD
    0x08004ae0   0x08004ae0   0x00000028   Code   RO         5985    i.HAL_UART_TxCpltCallback  app_uart.o
    0x08004b08   0x08004b08   0x00000002   Code   RO          677    i.HardFault_Handler  stm32f4xx_it.o
    0x08004b0a   0x08004b0a   0x0000002e   Code   RO         2368    i.I2C_IsAcknowledgeFailed  stm32f4xx_hal_i2c.o
    0x08004b38   0x08004b38   0x000000a8   Code   RO         2379    i.I2C_RequestMemoryWrite  stm32f4xx_hal_i2c.o
    0x08004be0   0x08004be0   0x00000056   Code   RO         2383    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004c36   0x08004c36   0x00000002   PAD
    0x08004c38   0x08004c38   0x00000090   Code   RO         2384    i.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004cc8   0x08004cc8   0x000000bc   Code   RO         2385    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004d84   0x08004d84   0x00000056   Code   RO         2387    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004dda   0x08004dda   0x00000002   PAD
    0x08004ddc   0x08004ddc   0x0000024c   Code   RO         5802    i.Key_Proc          app_key.o
    0x08005028   0x08005028   0x00000060   Code   RO         5803    i.Key_Read          app_key.o
    0x08005088   0x08005088   0x00000024   Code   RO         5840    i.LED_SHINE         app_led.o
    0x080050ac   0x080050ac   0x0000007c   Code   RO         5841    i.Led_Disp          app_led.o
    0x08005128   0x08005128   0x0000000c   Code   RO         5842    i.Led_Proc          app_led.o
    0x08005134   0x08005134   0x00000064   Code   RO          341    i.MX_ADC1_Init      adc.o
    0x08005198   0x08005198   0x0000006c   Code   RO          381    i.MX_DMA_Init       dma.o
    0x08005204   0x08005204   0x0000001c   Code   RO         6558    i.MX_FATFS_Init     fatfs.o
    0x08005220   0x08005220   0x0000011c   Code   RO          315    i.MX_GPIO_Init      gpio.o
    0x0800533c   0x0800533c   0x0000005c   Code   RO          407    i.MX_I2C1_Init      i2c.o
    0x08005398   0x08005398   0x00000084   Code   RO          449    i.MX_RTC_Init       rtc.o
    0x0800541c   0x0800541c   0x00000020   Code   RO          492    i.MX_SDIO_SD_Init   sdio.o
    0x0800543c   0x0800543c   0x00000048   Code   RO          534    i.MX_SPI2_Init      spi.o
    0x08005484   0x08005484   0x00000048   Code   RO          535    i.MX_SPI3_Init      spi.o
    0x080054cc   0x080054cc   0x00000068   Code   RO          582    i.MX_TIM3_Init      tim.o
    0x08005534   0x08005534   0x00000048   Code   RO          583    i.MX_TIM6_Init      tim.o
    0x0800557c   0x0800557c   0x00000064   Code   RO          630    i.MX_USART2_UART_Init  usart.o
    0x080055e0   0x080055e0   0x00000002   Code   RO          678    i.MemManage_Handler  stm32f4xx_it.o
    0x080055e2   0x080055e2   0x00000002   Code   RO          679    i.NMI_Handler       stm32f4xx_it.o
    0x080055e4   0x080055e4   0x00000034   Code   RO         5599    i.OLED_Clear        oled.o
    0x08005618   0x08005618   0x00000030   Code   RO         5602    i.OLED_Init         oled.o
    0x08005648   0x08005648   0x00000022   Code   RO         5603    i.OLED_Set_Position  oled.o
    0x0800566a   0x0800566a   0x00000002   PAD
    0x0800566c   0x0800566c   0x00000088   Code   RO         5604    i.OLED_ShowChar     oled.o
    0x080056f4   0x080056f4   0x00000036   Code   RO         5610    i.OLED_ShowStr      oled.o
    0x0800572a   0x0800572a   0x00000002   PAD
    0x0800572c   0x0800572c   0x00000024   Code   RO         5611    i.OLED_Write_cmd    oled.o
    0x08005750   0x08005750   0x00000024   Code   RO         5612    i.OLED_Write_data   oled.o
    0x08005774   0x08005774   0x0000002e   Code   RO         5882    i.Oled_Printf       app_oled.o
    0x080057a2   0x080057a2   0x00000002   Code   RO          680    i.PendSV_Handler    stm32f4xx_it.o
    0x080057a4   0x080057a4   0x00000048   Code   RO         5986    i.RS485_Transmit    app_uart.o
    0x080057ec   0x080057ec   0x00000012   Code   RO         2803    i.RTC_Bcd2ToByte    stm32f4xx_hal_rtc.o
    0x080057fe   0x080057fe   0x00000016   Code   RO         2804    i.RTC_ByteToBcd2    stm32f4xx_hal_rtc.o
    0x08005814   0x08005814   0x00000048   Code   RO         2805    i.RTC_EnterInitMode  stm32f4xx_hal_rtc.o
    0x0800585c   0x0800585c   0x0000002a   Code   RO         2806    i.RTC_ExitInitMode  stm32f4xx_hal_rtc.o
    0x08005886   0x08005886   0x00000002   PAD
    0x08005888   0x08005888   0x00000028   Code   RO         5923    i.RTC_Task          app_rtc.o
    0x080058b0   0x080058b0   0x00000024   Code   RO         3181    i.SDIO_ConfigData   stm32f4xx_ll_sdmmc.o
    0x080058d4   0x080058d4   0x00000008   Code   RO         3185    i.SDIO_GetPowerState  stm32f4xx_ll_sdmmc.o
    0x080058dc   0x080058dc   0x00000006   Code   RO         3186    i.SDIO_GetResponse  stm32f4xx_ll_sdmmc.o
    0x080058e2   0x080058e2   0x00000024   Code   RO         3187    i.SDIO_Init         stm32f4xx_ll_sdmmc.o
    0x08005906   0x08005906   0x00000008   Code   RO         3189    i.SDIO_PowerState_ON  stm32f4xx_ll_sdmmc.o
    0x0800590e   0x0800590e   0x00000006   Code   RO         3190    i.SDIO_ReadFIFO     stm32f4xx_ll_sdmmc.o
    0x08005914   0x08005914   0x00000020   Code   RO         3191    i.SDIO_SendCommand  stm32f4xx_ll_sdmmc.o
    0x08005934   0x08005934   0x0000000a   Code   RO         3193    i.SDIO_WriteFIFO    stm32f4xx_ll_sdmmc.o
    0x0800593e   0x0800593e   0x00000032   Code   RO         3194    i.SDMMC_CmdAppCommand  stm32f4xx_ll_sdmmc.o
    0x08005970   0x08005970   0x00000034   Code   RO         3195    i.SDMMC_CmdAppOperCommand  stm32f4xx_ll_sdmmc.o
    0x080059a4   0x080059a4   0x00000032   Code   RO         3196    i.SDMMC_CmdBlockLength  stm32f4xx_ll_sdmmc.o
    0x080059d6   0x080059d6   0x00000032   Code   RO         3197    i.SDMMC_CmdBusWidth  stm32f4xx_ll_sdmmc.o
    0x08005a08   0x08005a08   0x00000050   Code   RO         3201    i.SDMMC_CmdGoIdleState  stm32f4xx_ll_sdmmc.o
    0x08005a58   0x08005a58   0x00000030   Code   RO         3203    i.SDMMC_CmdOperCond  stm32f4xx_ll_sdmmc.o
    0x08005a88   0x08005a88   0x00000032   Code   RO         3204    i.SDMMC_CmdReadMultiBlock  stm32f4xx_ll_sdmmc.o
    0x08005aba   0x08005aba   0x00000032   Code   RO         3205    i.SDMMC_CmdReadSingleBlock  stm32f4xx_ll_sdmmc.o
    0x08005aec   0x08005aec   0x00000032   Code   RO         3208    i.SDMMC_CmdSelDesel  stm32f4xx_ll_sdmmc.o
    0x08005b1e   0x08005b1e   0x0000002c   Code   RO         3209    i.SDMMC_CmdSendCID  stm32f4xx_ll_sdmmc.o
    0x08005b4a   0x08005b4a   0x0000002c   Code   RO         3210    i.SDMMC_CmdSendCSD  stm32f4xx_ll_sdmmc.o
    0x08005b76   0x08005b76   0x00000030   Code   RO         3212    i.SDMMC_CmdSendSCR  stm32f4xx_ll_sdmmc.o
    0x08005ba6   0x08005ba6   0x00000032   Code   RO         3213    i.SDMMC_CmdSendStatus  stm32f4xx_ll_sdmmc.o
    0x08005bd8   0x08005bd8   0x00000030   Code   RO         3214    i.SDMMC_CmdSetRelAdd  stm32f4xx_ll_sdmmc.o
    0x08005c08   0x08005c08   0x00000034   Code   RO         3217    i.SDMMC_CmdStopTransfer  stm32f4xx_ll_sdmmc.o
    0x08005c3c   0x08005c3c   0x00000032   Code   RO         3219    i.SDMMC_CmdWriteMultiBlock  stm32f4xx_ll_sdmmc.o
    0x08005c6e   0x08005c6e   0x00000032   Code   RO         3220    i.SDMMC_CmdWriteSingleBlock  stm32f4xx_ll_sdmmc.o
    0x08005ca0   0x08005ca0   0x00000120   Code   RO         3221    i.SDMMC_GetCmdResp1  stm32f4xx_ll_sdmmc.o
    0x08005dc0   0x08005dc0   0x00000050   Code   RO         3222    i.SDMMC_GetCmdResp2  stm32f4xx_ll_sdmmc.o
    0x08005e10   0x08005e10   0x00000044   Code   RO         3223    i.SDMMC_GetCmdResp3  stm32f4xx_ll_sdmmc.o
    0x08005e54   0x08005e54   0x00000088   Code   RO         3224    i.SDMMC_GetCmdResp6  stm32f4xx_ll_sdmmc.o
    0x08005edc   0x08005edc   0x00000058   Code   RO         3225    i.SDMMC_GetCmdResp7  stm32f4xx_ll_sdmmc.o
    0x08005f34   0x08005f34   0x00000020   Code   RO         6498    i.SD_CheckStatus    sd_diskio.o
    0x08005f54   0x08005f54   0x00000060   Code   RO         6126    i.SD_Create_New_HideData_File  app_flash.o
    0x08005fb4   0x08005fb4   0x00000060   Code   RO         6127    i.SD_Create_New_OverLimit_File  app_flash.o
    0x08006014   0x08006014   0x0000005c   Code   RO         6128    i.SD_Create_New_Sample_File  app_flash.o
    0x08006070   0x08006070   0x000000e0   Code   RO         3499    i.SD_FindSCR        stm32f4xx_hal_sd.o
    0x08006150   0x08006150   0x00000070   Code   RO         6129    i.SD_Generate_DateTime_String  app_flash.o
    0x080061c0   0x080061c0   0x00000044   Code   RO         6130    i.SD_Get_Card_Memory_KB  app_flash.o
    0x08006204   0x08006204   0x00000060   Code   RO         6131    i.SD_Get_Power_On_Count  app_flash.o
    0x08006264   0x08006264   0x00000028   Code   RO         6132    i.SD_Increment_Power_On_Count  app_flash.o
    0x0800628c   0x0800628c   0x000000ee   Code   RO         3500    i.SD_InitCard       stm32f4xx_hal_sd.o
    0x0800637a   0x0800637a   0x00000002   PAD
    0x0800637c   0x0800637c   0x0000007c   Code   RO         6133    i.SD_Init_Folders   app_flash.o
    0x080063f8   0x080063f8   0x00000030   Code   RO         6134    i.SD_Init_Log_File  app_flash.o
    0x08006428   0x08006428   0x000000b4   Code   RO         3501    i.SD_PowerON        stm32f4xx_hal_sd.o
    0x080064dc   0x080064dc   0x00000010   Code   RO         6135    i.SD_Read_conf_Task  app_flash.o
    0x080064ec   0x080064ec   0x00000028   Code   RO         6136    i.SD_Reset_Power_On_Count  app_flash.o
    0x08006514   0x08006514   0x00000160   Code   RO         6137    i.SD_Save_HideData  app_flash.o
    0x08006674   0x08006674   0x0000011c   Code   RO         6138    i.SD_Save_OverLimit_Data  app_flash.o
    0x08006790   0x08006790   0x00000104   Code   RO         6139    i.SD_Save_Sample_Data  app_flash.o
    0x08006894   0x08006894   0x00000020   Code   RO         6140    i.SD_Test           app_flash.o
    0x080068b4   0x080068b4   0x00000074   Code   RO         6141    i.SD_Write_Log      app_flash.o
    0x08006928   0x08006928   0x000000e4   Code   RO         6142    i.SD_Write_Log_With_Timestamp  app_flash.o
    0x08006a0c   0x08006a0c   0x00000020   Code   RO         6499    i.SD_initialize     sd_diskio.o
    0x08006a2c   0x08006a2c   0x00000050   Code   RO         6500    i.SD_ioctl          sd_diskio.o
    0x08006a7c   0x08006a7c   0x00000026   Code   RO         6501    i.SD_read           sd_diskio.o
    0x08006aa2   0x08006aa2   0x00000004   Code   RO         6502    i.SD_status         sd_diskio.o
    0x08006aa6   0x08006aa6   0x00000002   PAD
    0x08006aa8   0x08006aa8   0x00000026   Code   RO         6503    i.SD_write          sd_diskio.o
    0x08006ace   0x08006ace   0x0000005c   Code   RO         3758    i.SPI_EndRxTransaction  stm32f4xx_hal_spi.o
    0x08006b2a   0x08006b2a   0x00000002   PAD
    0x08006b2c   0x08006b2c   0x0000006c   Code   RO         3759    i.SPI_EndRxTxTransaction  stm32f4xx_hal_spi.o
    0x08006b98   0x08006b98   0x000000bc   Code   RO         3764    i.SPI_WaitFlagStateUntilTimeout  stm32f4xx_hal_spi.o
    0x08006c54   0x08006c54   0x00000002   Code   RO          681    i.SVC_Handler       stm32f4xx_it.o
    0x08006c56   0x08006c56   0x00000002   PAD
    0x08006c58   0x08006c58   0x0000000c   Code   RO         6078    i.Schedular_Init    schedular.o
    0x08006c64   0x08006c64   0x00000044   Code   RO         6079    i.Schedular_Run     schedular.o
    0x08006ca8   0x08006ca8   0x00000004   Code   RO          682    i.SysTick_Handler   stm32f4xx_it.o
    0x08006cac   0x08006cac   0x000000a4   Code   RO           14    i.SystemClock_Config  main.o
    0x08006d50   0x08006d50   0x00000010   Code   RO         5479    i.SystemInit        system_stm32f4xx.o
    0x08006d60   0x08006d60   0x000000d0   Code   RO         4127    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08006e30   0x08006e30   0x00000014   Code   RO         4138    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08006e44   0x08006e44   0x00000010   Code   RO         4139    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08006e54   0x08006e54   0x00000022   Code   RO         4145    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08006e76   0x08006e76   0x00000024   Code   RO         4147    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08006e9a   0x08006e9a   0x0000000e   Code   RO         5048    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08006ea8   0x08006ea8   0x0000004a   Code   RO         5049    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08006ef2   0x08006ef2   0x00000086   Code   RO         5050    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08006f78   0x08006f78   0x0000001e   Code   RO         5052    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08006f96   0x08006f96   0x0000004e   Code   RO         5058    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08006fe4   0x08006fe4   0x0000001c   Code   RO         5059    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08007000   0x08007000   0x000000c2   Code   RO         5060    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x080070c2   0x080070c2   0x00000002   PAD
    0x080070c4   0x080070c4   0x0000010c   Code   RO         5061    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x080071d0   0x080071d0   0x000000a0   Code   RO         5062    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x08007270   0x08007270   0x00000036   Code   RO         5063    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x080072a6   0x080072a6   0x00000072   Code   RO         5064    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08007318   0x08007318   0x0000000c   Code   RO          683    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08007324   0x08007324   0x00000058   Code   RO         5987    i.Uart2_Proc        app_uart.o
    0x0800737c   0x0800737c   0x00000034   Code   RO         6080    i.Update_Sample_Cycle  schedular.o
    0x080073b0   0x080073b0   0x00000002   Code   RO          684    i.UsageFault_Handler  stm32f4xx_it.o
    0x080073b2   0x080073b2   0x00000002   PAD
    0x080073b4   0x080073b4   0x00000014   Code   RO         6144    i.Write_Log_With_Timestamp_Universal  app_flash.o
    0x080073c8   0x080073c8   0x00000034   Code   RO         7403    i.__0snprintf       mc_w.l(printfa.o)
    0x080073fc   0x080073fc   0x00000034   Code   RO         7407    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08007430   0x08007430   0x00000020   Code   RO         1912    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08007450   0x08007450   0x00000038   Code   RO         7120    i.__hardfp_atof     m_wm.l(atof.o)
    0x08007488   0x08007488   0x00000070   Code   RO         7126    i.__hardfp_floorf   m_wm.l(floorf.o)
    0x080074f8   0x080074f8   0x0000009a   Code   RO         7132    i.__hardfp_roundf   m_wm.l(roundf.o)
    0x08007592   0x08007592   0x00000002   PAD
    0x08007594   0x08007594   0x0000000c   Code   RO         7504    i.__read_errno      mc_w.l(errno.o)
    0x080075a0   0x080075a0   0x0000000e   Code   RO         7548    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080075ae   0x080075ae   0x00000002   Code   RO         7549    i.__scatterload_null  mc_w.l(handlers.o)
    0x080075b0   0x080075b0   0x0000000e   Code   RO         7550    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080075be   0x080075be   0x00000002   PAD
    0x080075c0   0x080075c0   0x0000000c   Code   RO         7505    i.__set_errno       mc_w.l(errno.o)
    0x080075cc   0x080075cc   0x00000184   Code   RO         7409    i._fp_digits        mc_w.l(printfa.o)
    0x08007750   0x08007750   0x0000000e   Code   RO         7464    i._is_digit         mc_w.l(scanf_fp.o)
    0x0800775e   0x0800775e   0x00000002   PAD
    0x08007760   0x08007760   0x000006b4   Code   RO         7410    i._printf_core      mc_w.l(printfa.o)
    0x08007e14   0x08007e14   0x00000024   Code   RO         7411    i._printf_post_padding  mc_w.l(printfa.o)
    0x08007e38   0x08007e38   0x0000002e   Code   RO         7412    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08007e66   0x08007e66   0x00000016   Code   RO         7413    i._snputc           mc_w.l(printfa.o)
    0x08007e7c   0x08007e7c   0x00000014   Code   RO         5717    i.adc_dma_init      app_adc.o
    0x08007e90   0x08007e90   0x00000070   Code   RO         6651    i.check_fs          ff.o
    0x08007f00   0x08007f00   0x00000010   Code   RO         6652    i.chk_chr           ff.o
    0x08007f10   0x08007f10   0x0000005c   Code   RO         6653    i.chk_lock          ff.o
    0x08007f6c   0x08007f6c   0x00000020   Code   RO         6654    i.clear_lock        ff.o
    0x08007f8c   0x08007f8c   0x00000024   Code   RO         6655    i.clmt_clust        ff.o
    0x08007fb0   0x08007fb0   0x00000018   Code   RO         6656    i.clust2sect        ff.o
    0x08007fc8   0x08007fc8   0x00000080   Code   RO         6657    i.cmp_lfn           ff.o
    0x08008048   0x08008048   0x000000a4   Code   RO         6658    i.create_chain      ff.o
    0x080080ec   0x080080ec   0x00000244   Code   RO         6659    i.create_name       ff.o
    0x08008330   0x08008330   0x00000088   Code   RO         5924    i.datetime_to_timestamp  app_rtc.o
    0x080083b8   0x080083b8   0x00000034   Code   RO         6660    i.dec_lock          ff.o
    0x080083ec   0x080083ec   0x000000d0   Code   RO         6661    i.dir_find          ff.o
    0x080084bc   0x080084bc   0x000000f4   Code   RO         6662    i.dir_next          ff.o
    0x080085b0   0x080085b0   0x000001e4   Code   RO         6664    i.dir_register      ff.o
    0x08008794   0x08008794   0x00000082   Code   RO         6666    i.dir_sdi           ff.o
    0x08008816   0x08008816   0x00000002   PAD
    0x08008818   0x08008818   0x0000002c   Code   RO         6596    i.disk_initialize   diskio.o
    0x08008844   0x08008844   0x0000001c   Code   RO         6597    i.disk_ioctl        diskio.o
    0x08008860   0x08008860   0x0000001c   Code   RO         6598    i.disk_read         diskio.o
    0x0800887c   0x0800887c   0x00000018   Code   RO         6599    i.disk_status       diskio.o
    0x08008894   0x08008894   0x0000001c   Code   RO         6600    i.disk_write        diskio.o
    0x080088b0   0x080088b0   0x00000028   Code   RO         6667    i.f_close           ff.o
    0x080088d8   0x080088d8   0x000000d4   Code   RO         6669    i.f_getfree         ff.o
    0x080089ac   0x080089ac   0x00000212   Code   RO         6671    i.f_lseek           ff.o
    0x08008bbe   0x08008bbe   0x00000002   PAD
    0x08008bc0   0x08008bc0   0x00000172   Code   RO         6672    i.f_mkdir           ff.o
    0x08008d32   0x08008d32   0x00000002   PAD
    0x08008d34   0x08008d34   0x00000058   Code   RO         6674    i.f_mount           ff.o
    0x08008d8c   0x08008d8c   0x00000244   Code   RO         6675    i.f_open            ff.o
    0x08008fd0   0x08008fd0   0x00000152   Code   RO         6680    i.f_read            ff.o
    0x08009122   0x08009122   0x0000008e   Code   RO         6684    i.f_sync            ff.o
    0x080091b0   0x080091b0   0x0000018e   Code   RO         6687    i.f_write           ff.o
    0x0800933e   0x0800933e   0x00000002   PAD
    0x08009340   0x08009340   0x0000004c   Code   RO         7091    i.ff_convert        cc936.o
    0x0800938c   0x0800938c   0x00000004   Code   RO         7055    i.ff_memalloc       syscall.o
    0x08009390   0x08009390   0x00000004   Code   RO         7056    i.ff_memfree        syscall.o
    0x08009394   0x08009394   0x00000080   Code   RO         7092    i.ff_wtoupper       cc936.o
    0x08009414   0x08009414   0x00000254   Code   RO         6688    i.find_volume       ff.o
    0x08009668   0x08009668   0x00000028   Code   RO         6145    i.flash_read        app_flash.o
    0x08009690   0x08009690   0x0000004c   Code   RO         6146    i.flash_write       app_flash.o
    0x080096dc   0x080096dc   0x00000074   Code   RO         6689    i.follow_path       ff.o
    0x08009750   0x08009750   0x00000050   Code   RO         7430    i.free              mc_w.l(malloc.o)
    0x080097a0   0x080097a0   0x0000008c   Code   RO         6690    i.gen_numname       ff.o
    0x0800982c   0x0800982c   0x000000be   Code   RO         6691    i.get_fat           ff.o
    0x080098ea   0x080098ea   0x00000004   Code   RO         6559    i.get_fattime       fatfs.o
    0x080098ee   0x080098ee   0x0000003c   Code   RO         6693    i.get_ldnumber      ff.o
    0x0800992a   0x0800992a   0x00000002   PAD
    0x0800992c   0x0800992c   0x00000080   Code   RO         6694    i.inc_lock          ff.o
    0x080099ac   0x080099ac   0x00000026   Code   RO         5925    i.is_leap_year      app_rtc.o
    0x080099d2   0x080099d2   0x00000026   Code   RO         6695    i.ld_clust          ff.o
    0x080099f8   0x080099f8   0x00000016   Code   RO         6696    i.ld_dword          ff.o
    0x08009a0e   0x08009a0e   0x0000000a   Code   RO         6697    i.ld_word           ff.o
    0x08009a18   0x08009a18   0x00000154   Code   RO           15    i.main              main.o
    0x08009b6c   0x08009b6c   0x0000006c   Code   RO         7431    i.malloc            mc_w.l(malloc.o)
    0x08009bd8   0x08009bd8   0x00000012   Code   RO         6698    i.mem_cpy           ff.o
    0x08009bea   0x08009bea   0x00000002   PAD
    0x08009bec   0x08009bec   0x0000000e   Code   RO         6699    i.mem_set           ff.o
    0x08009bfa   0x08009bfa   0x00000032   Code   RO         6700    i.move_window       ff.o
    0x08009c2c   0x08009c2c   0x000000dc   Code   RO         5883    i.oled_task         app_oled.o
    0x08009d08   0x08009d08   0x000000d8   Code   RO         5926    i.parse_rtc_datetime_string  app_rtc.o
    0x08009de0   0x08009de0   0x00000f5c   Code   RO         5988    i.parse_uart_command  app_uart.o
    0x0800ad3c   0x0800ad3c   0x00000264   Code   RO         5989    i.prase_rs485_command  app_uart.o
    0x0800afa0   0x0800afa0   0x000000ea   Code   RO         6702    i.put_fat           ff.o
    0x0800b08a   0x0800b08a   0x00000002   PAD
    0x0800b08c   0x0800b08c   0x000002a0   Code   RO         6147    i.read_config_file  app_flash.o
    0x0800b32c   0x0800b32c   0x000000d8   Code   RO         5990    i.read_config_from_flash  app_uart.o
    0x0800b404   0x0800b404   0x00000074   Code   RO         6705    i.remove_chain      ff.o
    0x0800b478   0x0800b478   0x0000002e   Code   RO         5991    i.rs485_printf      app_uart.o
    0x0800b4a6   0x0800b4a6   0x00000030   Code   RO         5513    i.rt_ringbuffer_data_len  ringbuffer.o
    0x0800b4d6   0x0800b4d6   0x00000074   Code   RO         5514    i.rt_ringbuffer_get  ringbuffer.o
    0x0800b54a   0x0800b54a   0x00000030   Code   RO         5516    i.rt_ringbuffer_init  ringbuffer.o
    0x0800b57a   0x0800b57a   0x00000078   Code   RO         5518    i.rt_ringbuffer_put  ringbuffer.o
    0x0800b5f2   0x0800b5f2   0x00000020   Code   RO         5523    i.rt_ringbuffer_status  ringbuffer.o
    0x0800b612   0x0800b612   0x00000040   Code   RO         5927    i.rtc_to_unix_timestamp  app_rtc.o
    0x0800b652   0x0800b652   0x00000002   PAD
    0x0800b654   0x0800b654   0x0000008c   Code   RO         5992    i.save_config_to_flash  app_uart.o
    0x0800b6e0   0x0800b6e0   0x0000005c   Code   RO         5368    i.spi_flash_buffer_read  gd25qxx.o
    0x0800b73c   0x0800b73c   0x00000010   Code   RO         5371    i.spi_flash_init    gd25qxx.o
    0x0800b74c   0x0800b74c   0x00000064   Code   RO         5372    i.spi_flash_page_write  gd25qxx.o
    0x0800b7b0   0x0800b7b0   0x00000050   Code   RO         5374    i.spi_flash_read_id  gd25qxx.o
    0x0800b800   0x0800b800   0x0000004c   Code   RO         5376    i.spi_flash_sector_erase  gd25qxx.o
    0x0800b84c   0x0800b84c   0x00000020   Code   RO         5377    i.spi_flash_send_byte  gd25qxx.o
    0x0800b86c   0x0800b86c   0x00000022   Code   RO         5380    i.spi_flash_verify_communication  gd25qxx.o
    0x0800b88e   0x0800b88e   0x00000002   PAD
    0x0800b890   0x0800b890   0x00000034   Code   RO         5381    i.spi_flash_wait_for_write_end  gd25qxx.o
    0x0800b8c4   0x0800b8c4   0x0000002c   Code   RO         5382    i.spi_flash_write_enable  gd25qxx.o
    0x0800b8f0   0x0800b8f0   0x00000028   Code   RO         6706    i.st_clust          ff.o
    0x0800b918   0x0800b918   0x00000010   Code   RO         6707    i.st_dword          ff.o
    0x0800b928   0x0800b928   0x00000008   Code   RO         6708    i.st_word           ff.o
    0x0800b930   0x0800b930   0x0000001a   Code   RO         6709    i.sum_sfn           ff.o
    0x0800b94a   0x0800b94a   0x00000002   PAD
    0x0800b94c   0x0800b94c   0x00000088   Code   RO         6710    i.sync_fs           ff.o
    0x0800b9d4   0x0800b9d4   0x00000052   Code   RO         6711    i.sync_window       ff.o
    0x0800ba26   0x0800ba26   0x00000002   PAD
    0x0800ba28   0x0800ba28   0x000001ac   Code   RO         5993    i.system_selftest   app_uart.o
    0x0800bbd4   0x0800bbd4   0x0000003c   Code   RO         5928    i.timestamp_to_hex  app_rtc.o
    0x0800bc10   0x0800bc10   0x00000034   Code   RO         6712    i.validate          ff.o
    0x0800bc44   0x0800bc44   0x00000068   Code   RO         5718    i.voltage_to_hex    app_adc.o
    0x0800bcac   0x0800bcac   0x00000008   Data   RO         1608    .constdata          stm32f4xx_hal_dma.o
    0x0800bcb4   0x0800bcb4   0x00000010   Data   RO         5480    .constdata          system_stm32f4xx.o
    0x0800bcc4   0x0800bcc4   0x00000008   Data   RO         5481    .constdata          system_stm32f4xx.o
    0x0800bccc   0x0800bccc   0x00000a98   Data   RO         5613    .constdata          oled.o
    0x0800c764   0x0800c764   0x0000000c   Data   RO         5930    .constdata          app_rtc.o
    0x0800c770   0x0800c770   0x00000014   Data   RO         6504    .constdata          sd_diskio.o
    0x0800c784   0x0800c784   0x0000002a   Data   RO         6714    .constdata          ff.o
    0x0800c7ae   0x0800c7ae   0x0002abb6   Data   RO         7093    .constdata          cc936.o
    0x08037364   0x08037364   0x00000040   Data   RO         7544    .constdata          mc_w.l(ctype_c.o)
    0x080373a4   0x080373a4   0x00000020   Data   RO         7546    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080373c4, Size: 0x00003d20, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080373c4   0x0000000c   Data   RW         2073    .data               stm32f4xx_hal.o
    0x2000000c   0x080373d0   0x00000004   Data   RW         5482    .data               system_stm32f4xx.o
    0x20000010   0x080373d4   0x00000016   Data   RW         5614    .data               oled.o
    0x20000026   0x080373ea   0x00000002   PAD
    0x20000028   0x080373ec   0x0000000c   Data   RW         5720    .data               app_adc.o
    0x20000034   0x080373f8   0x00000004   Data   RW         5804    .data               app_key.o
    0x20000038   0x080373fc   0x00000007   Data   RW         5843    .data               app_led.o
    0x2000003f   0x08037403   0x00000001   Data   RW         5884    .data               app_oled.o
    0x20000040   0x08037404   0x00000004   Data   RW         5931    .data               app_rtc.o
    0x20000044   0x08037408   0x00000004   PAD
    0x20000048   0x0803740c   0x00000048   Data   RW         5996    .data               app_uart.o
    0x20000090   0x08037454   0x00000074   Data   RW         6081    .data               schedular.o
    0x20000104   0x080374c8   0x00000001   Data   RW         6505    .data               sd_diskio.o
    0x20000105   0x080374c9   0x00000005   Data   RW         6562    .data               fatfs.o
    0x2000010a   0x080374ce   0x00000002   PAD
    0x2000010c   0x080374d0   0x00000008   Data   RW         6715    .data               ff.o
    0x20000114   0x080374d8   0x00000004   Data   RW         7162    .data               mc_w.l(strtok.o)
    0x20000118   0x080374dc   0x00000004   Data   RW         7501    .data               mc_w.l(mvars.o)
    0x2000011c   0x080374e0   0x00000004   Data   RW         7502    .data               mc_w.l(mvars.o)
    0x20000120   0x080374e4   0x00000004   Data   RW         7506    .data               mc_w.l(errno.o)
    0x20000124        -       0x000000a8   Zero   RW          342    .bss                adc.o
    0x200001cc        -       0x00000054   Zero   RW          408    .bss                i2c.o
    0x20000220        -       0x00000020   Zero   RW          450    .bss                rtc.o
    0x20000240        -       0x00000084   Zero   RW          493    .bss                sdio.o
    0x200002c4        -       0x00000170   Zero   RW          536    .bss                spi.o
    0x20000434        -       0x00000090   Zero   RW          584    .bss                tim.o
    0x200004c4        -       0x000000a8   Zero   RW          631    .bss                usart.o
    0x2000056c        -       0x00000094   Zero   RW         5719    .bss                app_adc.o
    0x20000600        -       0x00000014   Zero   RW         5929    .bss                app_rtc.o
    0x20000614        -       0x0000018c   Zero   RW         5994    .bss                app_uart.o
    0x200007a0        -       0x00000080   Zero   RW         5995    .bss                app_uart.o
    0x20000820        -       0x000002a0   Zero   RW         6148    .bss                app_flash.o
    0x20000ac0        -       0x00000234   Zero   RW         6560    .bss                fatfs.o
    0x20000cf4        -       0x00000020   Zero   RW         6713    .bss                ff.o
    0x20000d14        -       0x0000000c   Zero   RW         7009    .bss                ff_gen_drv.o
    0x20000d20        -       0x00001000   Zero   RW            2    HEAP                startup_stm32f427xx.o
    0x20001d20        -       0x00002000   Zero   RW            1    STACK               startup_stm32f427xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       284         36          0          0        168       1755   adc.o
       876        186          0         12        148       5414   app_adc.o
       348        104          0          0          0       3536   app_extspi.o
      2908        826          0          0        672      19214   app_flash.o
       684        376          0          4          0       1702   app_key.o
       172         24          0          7          0       2114   app_led.o
       266         94          0          1          0       2218   app_oled.o
       554         88         12          4         20       5182   app_rtc.o
      5746       3058          0         72        524      11389   app_uart.o
       162         26          0          0          0       3940   bsp_driver_sd.o
       204         26     175030          0          0       2435   cc936.o
       152         24          0          0          0       3649   diskio.o
       108          4          0          0          0        802   dma.o
        32         10          0          5        564       1456   fatfs.o
      7092         94         42          8         32      47294   ff.o
        70          6          0          0         12       1583   ff_gen_drv.o
       526         42          0          0          0       6096   gd25qxx.o
       284         22          0          0          0       1055   gpio.o
       200         28          0          0         84       1637   i2c.o
       508        130          0          0          0     813105   main.o
       396         24       2712         22          0       5345   oled.o
       364          0          0          0          0       6139   ringbuffer.o
       192         16          0          0         32       1676   rtc.o
       132         14          0        116          0       2287   schedular.o
       224         14         20          1          0       4396   sd_diskio.o
       180         26          0          0        132       1601   sdio.o
       412         46          0          0        368       2492   spi.o
        36          8        428          0      12288        820   startup_stm32f427xx.o
       180         28          0         12          0       9429   stm32f4xx_hal.o
      1228         72          0          0          0       7357   stm32f4xx_hal_adc.o
       198         14          0          0          0      33735   stm32f4xx_hal_cortex.o
      1084         16          8          0          0       7202   stm32f4xx_hal_dma.o
       584         54          0          0          0       2803   stm32f4xx_hal_gpio.o
      1414         32          0          0          0       9495   stm32f4xx_hal_i2c.o
       128          0          0          0          0       1811   stm32f4xx_hal_i2c_ex.o
        48          6          0          0          0        826   stm32f4xx_hal_msp.o
       112         14          0          0          0       1256   stm32f4xx_hal_pwr_ex.o
      1348         76          0          0          0       5164   stm32f4xx_hal_rcc.o
       596         36          0          0          0       1580   stm32f4xx_hal_rcc_ex.o
       862         36          0          0          0       8009   stm32f4xx_hal_rtc.o
      2496         32          0          0          0      12308   stm32f4xx_hal_sd.o
      1770         16          0          0          0       8290   stm32f4xx_hal_spi.o
       624         44          0          0          0       6203   stm32f4xx_hal_tim.o
       144         28          0          0          0       1344   stm32f4xx_hal_tim_ex.o
      2266         28          0          0          0      15762   stm32f4xx_hal_uart.o
        80         30          0          0          0       6322   stm32f4xx_it.o
      1668         48          0          0          0      23103   stm32f4xx_ll_sdmmc.o
         8          0          0          0          0       1619   syscall.o
        16          4         24          4          0       1079   system_stm32f4xx.o
       240         30          0          0        144       2215   tim.o
       276         38          0          0        168       1773   usart.o

    ----------------------------------------------------------------------
     40552       <USER>     <GROUP>        276      15356    1129017   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        70          0          0          8          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        56         12          0          0          0        132   atof.o
       112         14          0          0          0        116   floorf.o
       154          0          0          0          0        140   roundf.o
        56          8          0          0          0         84   __0sscanf.o
        28          0          0          0          0         68   _chval.o
       816          6          0          0          0        112   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        40          6         64          0          0         68   ctype_c.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        24         12          0          4          0        136   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        10          0          0          0          0         68   isspace_c.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
       188         20          0          0          0        160   malloc.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
         0          0          0          8          0          0   mvars.o
      2312         90          0          0          0        616   printfa.o
        40          8          0          0          0         84   scanf_char.o
       878         12          0          0          0        216   scanf_fp.o
        20          0          0          0          0         68   strchr.o
        28          0          0          0          0         76   strcmp.o
        18          0          0          0          0         68   strcpy.o
        14          0          0          0          0         68   strlen.o
        30          0          0          0          0         80   strncmp.o
        24          0          0          0          0         76   strncpy.o
        36          0          0          0          0         80   strstr.o
       156         12          0          0          0        120   strtod.o
        68          6          0          4          0         80   strtok.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        24          0          0          0          0         76   dfltul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o
        60          0          0          0          0         76   frnd.o

    ----------------------------------------------------------------------
      7320        <USER>         <GROUP>         16          0       4716   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       322         26          0          0          0        388   m_wm.l
      5584        196         64         16          0       3128   mc_w.l
      1402          0          0          0          0       1200   mf_w.l

    ----------------------------------------------------------------------
      7320        <USER>         <GROUP>         16          0       4716   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     47872       6256     178372        292      15356    1100245   Grand Totals
     47872       6256     178372        292      15356    1100245   ELF Image Totals
     47872       6256     178372        292          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)               226244 ( 220.94kB)
    Total RW  Size (RW Data + ZI Data)             15648 (  15.28kB)
    Total ROM Size (Code + RO Data + RW Data)     226536 ( 221.23kB)

==============================================================================

