# STM32 数据采集与存储系统 - 代码使用手册

## 目录
- [系统概述](#系统概述)
- [硬件配置](#硬件配置)
- [核心模块](#核心模块)
- [函数接口](#函数接口)
- [数据结构](#数据结构)
- [命令系统](#命令系统)
- [配置管理](#配置管理)
- [日志系统](#日志系统)
- [存储系统](#存储系统)
- [通信协议](#通信协议)
- [使用示例](#使用示例)

## 系统概述

本系统是基于STM32F407的数据采集与存储系统，具备以下主要功能：
- ADC电压采集与监控
- SD卡数据存储
- Flash日志记录
- UART/RS485通信
- RTC时间管理
- 任务调度系统
- 配置参数管理

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UART通信      │    │   ADC采集       │    │   存储系统      │
│   - UART1       │    │   - 电压监控    │    │   - SD卡        │
│   - UART2/RS485 │    │   - 阈值报警    │    │   - Flash日志   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   任务调度器    │
                    │   - 10ms周期    │
                    │   - 多任务管理  │
                    └─────────────────┘
```

## 硬件配置

### 引脚分配
| 功能 | 引脚 | 配置 | 说明 |
|------|------|------|------|
| UART1_TX | PA9 | AF7 | 调试串口发送 |
| UART1_RX | PA10 | AF7 | 调试串口接收 |
| UART2_TX | PA2 | AF7 | RS485 A+ |
| UART2_RX | PA3 | AF7 | RS485 B- |
| RS485_DE_RE | PA4 | OUTPUT | RS485方向控制 |
| ADC1_IN0 | PA0 | ANALOG | 电压采集 |
| SPI1_SCK | PA5 | AF5 | SD卡时钟 |
| SPI1_MISO | PA6 | AF5 | SD卡数据输入 |
| SPI1_MOSI | PA7 | AF5 | SD卡数据输出 |
| SD_CS | PB6 | OUTPUT | SD卡片选 |

### 时钟配置
- 系统时钟：168MHz
- APB1时钟：42MHz
- APB2时钟：84MHz
- ADC时钟：21MHz

## 核心模块

### 1. 任务调度器 (Schedular.c)

#### 任务结构体
```c
typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t period;           // 执行周期(ms)
    uint32_t last_run;         // 上次执行时间
} Task_t;
```

#### 主要函数
- `Task_Scheduler()` - 主调度器，在main循环中调用
- 支持多任务并发执行，基于时间片轮转

#### 当前任务列表
| 任务 | 周期 | 功能 |
|------|------|------|
| Uart_Proc | 10ms | UART1数据处理 |
| Uart2_Proc | 10ms | UART2/RS485数据处理 |
| ADC_Proc | 100ms | ADC数据处理 |
| ADC_TASK | 1000ms | ADC采集任务 |
| ADC_TASK_HIDE | 1000ms | 加密模式ADC任务 |
| ADC_Data_Storage_Task | 60000ms | 数据存储任务 |

### 2. ADC模块 (APP_ADC.c)

#### 核心变量
```c
extern float Ratio;              // 电压比例系数 (0-100)
extern float limit;              // 报警阈值 (0-200V)
extern uint8_t Led_ADC_Mode;     // 采集模式标志
extern uint8_t Hide_Mode;        // 加密模式标志
```

#### 主要函数

##### `Get_ADC_Voltage()`
```c
float Get_ADC_Voltage(void);
```
- **功能**: 获取当前ADC电压值
- **返回**: 电压值(V)
- **说明**: 自动应用比例系数计算

##### `ADC_Proc()`
```c
void ADC_Proc(void);
```
- **功能**: ADC数据处理，检查阈值报警
- **调用周期**: 100ms
- **功能**: 超限时触发报警日志

##### `ADC_TASK()`
```c
void ADC_TASK(void);
```
- **功能**: 标准ADC采集任务
- **调用周期**: 1000ms
- **输出**: 通过UART1输出电压值

##### `ADC_TASK_HIDE()`
```c
void ADC_TASK_HIDE(void);
```
- **功能**: 加密模式ADC任务
- **调用周期**: 1000ms
- **特点**: 输出加密格式数据

##### `ADC_Data_Storage_Task()`
```c
void ADC_Data_Storage_Task(void);
```
- **功能**: 数据存储任务
- **调用周期**: 60秒
- **功能**: 将数据保存到SD卡

### 3. UART通信模块 (APP_Uart.c)

#### 环形缓冲区
```c
struct rt_ringbuffer uart_ringbuffer;      // UART1缓冲区
struct rt_ringbuffer uart2_ringbuffer;     // UART2缓冲区
uint8_t ringbuffer_pool[128];              // UART1缓冲池
uint8_t uart2_ringbuffer_pool[128];        // UART2缓冲池
```

#### 主要函数

##### `my_printf()`
```c
int my_printf(UART_HandleTypeDef *huart, const char *format, ...);
```
- **功能**: 格式化串口输出
- **参数**: 
  - `huart`: UART句柄
  - `format`: 格式化字符串
- **返回**: 发送字节数

##### `Uart_Proc()`
```c
void Uart_Proc(void);
```
- **功能**: UART1数据处理
- **调用周期**: 10ms
- **功能**: 解析命令并执行

##### `parse_uart_command()`
```c
void parse_uart_command(uint8_t *buffer, uint16_t length);
```
- **功能**: 命令解析器
- **支持命令**: 见[命令系统](#命令系统)章节

#### RS485功能

##### `RS485_Transmit()`
```c
HAL_StatusTypeDef RS485_Transmit(uint8_t *data, uint16_t size, uint32_t timeout);
```
- **功能**: RS485数据发送
- **特点**: 自动控制DE/RE引脚
- **参数**:
  - `data`: 发送数据
  - `size`: 数据长度
  - `timeout`: 超时时间

##### `rs485_printf()`
```c
int rs485_printf(const char *format, ...);
```
- **功能**: RS485格式化输出
- **用法**: 类似printf，通过RS485发送

### 4. 存储系统

#### SD卡模块 (APP_SD.c)

##### `SD_Test()`
```c
uint8_t SD_Test(void);
```
- **功能**: SD卡功能测试
- **返回**: 0=成功, 1=失败
- **测试项**: 挂载、读写、文件操作

##### `Write_Data_To_SD()`
```c
uint8_t Write_Data_To_SD(const char* filename, const char* data);
```
- **功能**: 写入数据到SD卡
- **参数**:
  - `filename`: 文件名
  - `data`: 写入数据
- **特点**: 自动添加时间戳

#### Flash日志模块 (APP_FLASH.c)

##### 日志头结构
```c
typedef struct {
    uint32_t magic;           // 魔数标识
    uint32_t log_count;       // 日志条目数
    uint32_t write_offset;    // 写入偏移
    uint8_t is_transferred;   // 转存标志
    uint8_t reserved[15];     // 保留字段
} FlashLogHeader_t;
```

##### `Flash_Log_Init()`
```c
uint8_t Flash_Log_Init(void);
```
- **功能**: 初始化Flash日志系统
- **返回**: 0=成功, 1=失败

##### `Write_Log_With_Timestamp_Universal()`
```c
uint8_t Write_Log_With_Timestamp_Universal(const char* log_message);
```
- **功能**: 写入带时间戳的日志
- **参数**: `log_message` - 日志内容
- **特点**: 自动添加RTC时间戳

##### `Flash_Transfer_Logs_To_SD()`
```c
uint8_t Flash_Transfer_Logs_To_SD(void);
```
- **功能**: 将Flash日志转存到SD卡
- **文件**: 保存为log.txt
- **特点**: 转存后清空Flash日志

### 5. 配置管理 (APP_Config.c)

#### 配置结构体
```c
typedef struct {
    float ratio;              // 电压比例
    float limit;              // 报警阈值
    uint8_t led_adc_mode;     // 采集模式
    uint8_t hide_mode;        // 加密模式
    uint32_t checksum;        // 校验和
} Config_t;
```

#### 主要函数

##### `Config_Save_To_Flash()`
```c
uint8_t Config_Save_To_Flash(void);
```
- **功能**: 保存配置到Flash
- **地址**: 0x08060000
- **返回**: 0=成功, 1=失败

##### `Config_Load_From_Flash()`
```c
uint8_t Config_Load_From_Flash(void);
```
- **功能**: 从Flash加载配置
- **特点**: 自动校验数据完整性

##### `Config_Reset_To_Default()`
```c
void Config_Reset_To_Default(void);
```
- **功能**: 恢复默认配置
- **默认值**:
  - ratio: 50.0
  - limit: 100.0
  - led_adc_mode: 0
  - hide_mode: 0

### 6. RTC时间管理 (APP_RTC.c)

#### 主要函数

##### `RTC_Config_Time()`
```c
uint8_t RTC_Config_Time(uint8_t hour, uint8_t minute, uint8_t second);
```
- **功能**: 配置RTC时间
- **参数**: 时、分、秒
- **返回**: 0=成功, 1=失败

##### `RTC_Config_Date()`
```c
uint8_t RTC_Config_Date(uint8_t year, uint8_t month, uint8_t date, uint8_t weekday);
```
- **功能**: 配置RTC日期
- **参数**: 年、月、日、星期
- **返回**: 0=成功, 1=失败

##### `Get_Timestamp_String()`
```c
void Get_Timestamp_String(char* buffer, size_t buffer_size);
```
- **功能**: 获取时间戳字符串
- **格式**: "YYYY-MM-DD HH:MM:SS"
- **参数**: 
  - `buffer`: 输出缓冲区
  - `buffer_size`: 缓冲区大小

## 数据结构

### 1. 环形缓冲区 (rt_ringbuffer)
```c
struct rt_ringbuffer {
    uint8_t *buffer_ptr;      // 缓冲区指针
    uint16_t read_mirror : 1; // 读镜像位
    uint16_t read_index : 15; // 读索引
    uint16_t write_mirror : 1;// 写镜像位
    uint16_t write_index : 15;// 写索引
    uint16_t buffer_size;     // 缓冲区大小
};
```

#### 相关函数
- `rt_ringbuffer_init()` - 初始化
- `rt_ringbuffer_put()` - 写入数据
- `rt_ringbuffer_get()` - 读取数据
- `rt_ringbuffer_data_len()` - 获取数据长度

### 2. 任务结构体
```c
typedef struct {
    void (*task_func)(void);  // 任务函数指针
    uint32_t period;         // 执行周期(ms)
    uint32_t last_run;       // 上次执行时间
} Task_t;
```

### 3. Flash日志头
```c
typedef struct {
    uint32_t magic;          // 魔数: 0x12345678
    uint32_t log_count;      // 日志条目数
    uint32_t write_offset;   // 当前写入偏移
    uint8_t is_transferred;  // 是否已转存
    uint8_t reserved[15];    // 保留字段
} FlashLogHeader_t;
```

## 命令系统

### UART1命令列表

| 命令 | 参数 | 功能 | 示例 |
|------|------|------|------|
| `test` | 无 | 系统自检 | `test` |
| `RTC Config` | HH:MM:SS | 设置时间 | `RTC Config 14:30:25` |
| `RTC now` | 无 | 显示当前时间 | `RTC now` |
| `ratio` | 0-100 | 设置电压比例 | `ratio 75.5` |
| `limit` | 0-200 | 设置报警阈值 | `limit 120.0` |
| `start` | 无 | 开始采集 | `start` |
| `stop` | 无 | 停止采集 | `stop` |
| `hide` | 无 | 启用加密模式 | `hide` |
| `unhide` | 无 | 禁用加密模式 | `unhide` |
| `config save` | 无 | 保存配置 | `config save` |
| `rs485 test` | 无 | 测试RS485 | `rs485 test` |
| `rs485 status` | 无 | 发送状态 | `rs485 status` |
| `sd test` | 无 | 测试SD卡 | `sd test` |
| `help` | 无 | 显示帮助 | `help` |

### RS485命令列表

| 命令 | 响应 | 功能 |
|------|------|------|
| `RS485_TEST` | `RS485_OK` | 通信测试 |
| `RS485_STATUS` | 状态信息 | 获取系统状态 |
| `RS485_DATA` | 数据信息 | 获取实时数据 |

## 配置管理

### 配置参数

| 参数 | 类型 | 范围 | 默认值 | 说明 |
|------|------|------|--------|------|
| ratio | float | 0-100 | 50.0 | 电压比例系数 |
| limit | float | 0-200 | 100.0 | 报警阈值(V) |
| led_adc_mode | uint8_t | 0/1 | 0 | 采集模式开关 |
| hide_mode | uint8_t | 0/1 | 0 | 加密模式开关 |

### 配置操作

#### 保存配置
```c
// 修改参数
Ratio = 75.5;
limit = 120.0;

// 保存到Flash
if (Config_Save_To_Flash() == 0) {
    printf("配置保存成功\n");
}
```

#### 加载配置
```c
// 系统启动时自动加载
if (Config_Load_From_Flash() != 0) {
    // 加载失败，使用默认配置
    Config_Reset_To_Default();
}
```

## 日志系统

### 日志级别
- **INFO**: 一般信息
- **WARNING**: 警告信息  
- **ERROR**: 错误信息
- **ALARM**: 报警信息

### 日志格式
```
[YYYY-MM-DD HH:MM:SS] 日志内容
```

### 使用方法

#### 写入日志
```c
// 写入普通日志
Write_Log_With_Timestamp_Universal("system started");

// 写入报警日志
Write_Log_With_Timestamp_Universal("voltage over limit: 125.5V");
```

#### 日志转存
```c
// 手动转存Flash日志到SD卡
if (Flash_Transfer_Logs_To_SD() == 0) {
    printf("日志转存成功\n");
}
```

### 存储位置
- **Flash**: 临时存储，断电保持
- **SD卡**: 长期存储，文件名为log.txt

## 存储系统

### Flash存储映射

| 地址 | 大小 | 用途 |
|------|------|------|
| 0x08060000 | 4KB | 配置参数 |
| 0x08061000 | 32B | 日志头 |
| 0x08061020 | 4KB | 日志数据 |

### SD卡文件结构
```
/
├── log.txt          # 系统日志
├── data_YYYYMMDD.txt # 每日数据文件
└── config_backup.txt # 配置备份
```

### 数据格式

#### 电压数据文件
```
[2024-01-15 14:30:25] Voltage: 125.5V, Ratio: 75.5, Mode: SAMPLING
[2024-01-15 14:31:25] Voltage: 126.2V, Ratio: 75.5, Mode: SAMPLING
```

#### 加密模式数据
```
[2024-01-15 14:30:25] Data: 7D5F8A2B, Hash: A1B2C3D4
[2024-01-15 14:31:25] Data: 8E6F9C3A, Hash: B2C3D4E5
```

## 通信协议

### UART1协议 (调试接口)
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **流控**: 无

### UART2/RS485协议
- **波特率**: 115200  
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **模式**: 半双工
- **控制**: PA4 (DE/RE)

#### RS485通信时序
```
发送: DE/RE=1 → 发送数据 → 等待完成 → DE/RE=0
接收: DE/RE=0 → 等待数据 → 处理数据
```

## 使用示例

### 1. 基本系统启动
```c
int main(void) {
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    
    // 外设初始化
    MX_GPIO_Init();
    MX_USART1_UART_Init();
    MX_USART2_UART_Init();
    MX_ADC1_Init();
    MX_RTC_Init();
    
    // 应用初始化
    rt_ringbuffer_init(&uart_ringbuffer, ringbuffer_pool, sizeof(ringbuffer_pool));
    rt_ringbuffer_init(&uart2_ringbuffer, uart2_ringbuffer_pool, sizeof(uart2_ringbuffer_pool));
    
    // 启动DMA接收
    HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
    HAL_UARTEx_ReceiveToIdle_DMA(&huart2, uart2_rx_dma_buffer, sizeof(uart2_rx_dma_buffer));
    
    // 加载配置
    if (Config_Load_From_Flash() != 0) {
        Config_Reset_To_Default();
    }
    
    // 初始化日志系统
    Flash_Log_Init();
    Write_Log_With_Timestamp_Universal("system started");
    
    // 主循环
    while (1) {
        Task_Scheduler();
    }
}
```

### 2. 电压监控示例
```c
void voltage_monitor_example(void) {
    // 设置监控参数
    Ratio = 80.0;  // 设置比例系数
    limit = 110.0; // 设置报警阈值
    
    // 开始采集
    Led_ADC_Mode = 1;
    
    // 获取当前电压
    float voltage = Get_ADC_Voltage();
    
    // 检查是否超限
    if (voltage > limit) {
        char alarm_msg[64];
        snprintf(alarm_msg, sizeof(alarm_msg), "voltage alarm: %.2fV > %.2fV", voltage, limit);
        Write_Log_With_Timestamp_Universal(alarm_msg);
    }
    
    // 保存配置
    Config_Save_To_Flash();
}
```

### 3. RS485通信示例
```c
void rs485_communication_example(void) {
    // 发送测试消息
    char test_msg[] = "RS485_TEST";
    if (RS485_Transmit((uint8_t*)test_msg, strlen(test_msg), 100) == HAL_OK) {
        printf("RS485发送成功\n");
    }
    
    // 格式化发送
    rs485_printf("STATUS:VOLTAGE=%.2f,TIME=%02d:%02d:%02d\r\n", 
                 Get_ADC_Voltage(), Time.Hours, Time.Minutes, Time.Seconds);
}
```

### 4. 数据存储示例
```c
void data_storage_example(void) {
    char data_buffer[128];
    char timestamp[32];
    
    // 获取时间戳
    Get_Timestamp_String(timestamp, sizeof(timestamp));
    
    // 格式化数据
    snprintf(data_buffer, sizeof(data_buffer), 
             "[%s] Voltage: %.2fV, Ratio: %.2f, Mode: %s\r\n",
             timestamp, Get_ADC_Voltage(), Ratio, 
             (Led_ADC_Mode ? "SAMPLING" : "IDLE"));
    
    // 写入SD卡
    char filename[32];
    snprintf(filename, sizeof(filename), "data_%04d%02d%02d.txt", 
             Date.Year + 2000, Date.Month, Date.Date);
    
    if (Write_Data_To_SD(filename, data_buffer) == 0) {
        printf("数据保存成功\n");
    }
}
```

### 5. 命令处理示例
```c
void command_processing_example(void) {
    // 通过UART1发送命令
    // 示例命令序列:
    
    // 1. 设置时间
    // 发送: "RTC Config 14:30:25"
    
    // 2. 设置参数
    // 发送: "ratio 75.5"
    // 发送: "limit 120.0"
    
    // 3. 开始采集
    // 发送: "start"
    
    // 4. 保存配置
    // 发送: "config save"
    
    // 5. 测试RS485
    // 发送: "rs485 test"
    
    // 6. 查看帮助
    // 发送: "help"
}
```

## 故障排除

### 常见问题

#### 1. SD卡无法识别
- 检查SPI连接
- 确认SD卡格式为FAT32
- 检查供电电压

#### 2. RS485通信失败
- 检查DE/RE控制引脚
- 确认差分信号连接
- 检查波特率设置

#### 3. ADC读数异常
- 检查参考电压
- 确认ADC通道配置
- 检查比例系数设置

#### 4. 配置丢失
- 检查Flash写入权限
- 确认地址映射正确
- 验证校验和计算

### 调试方法

#### 1. 串口调试
```c
// 启用详细日志
my_printf(&huart1, "Debug: ADC Value = %d\r\n", adc_value);
my_printf(&huart1, "Debug: Voltage = %.2fV\r\n", voltage);
```

#### 2. LED指示
```c
// 使用LED指示系统状态
HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_SET);   // 正常
HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, GPIO_PIN_RESET); // 异常
```

#### 3. 日志分析
```c
// 记录关键操作
Write_Log_With_Timestamp_Universal("config loaded successfully");
Write_Log_With_Timestamp_Universal("sd card mount failed");
```

## 版本信息

- **版本**: v1.0.0
- **日期**: 2024-01-15
- **作者**: STM32开发团队
- **MCU**: STM32F407VGT6
- **IDE**: STM32CubeIDE
- **HAL库**: v1.7.0

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 实现基本ADC采集功能
- 添加SD卡存储支持
- 实现UART命令系统
- 添加RS485通信功能
- 实现Flash配置管理
- 添加RTC时间管理
- 实现任务调度系统

---

*本手册详细描述了系统的所有功能模块和使用方法，如有疑问请参考源代码或联系开发团队。*