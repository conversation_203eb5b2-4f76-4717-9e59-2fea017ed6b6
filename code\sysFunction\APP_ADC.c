#include "APP_ADC.h"
#include <stdlib.h> // 用于strtol函数

// --- 全局变量 ---
#define ADC_DMA_BUFFER_SIZE 32                // DMA缓冲区大小，可以根据需要调整
uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE]; // DMA 目标缓冲区
__IO uint32_t adc_val;                        // 用于存储计算后的平均 ADC 值
__IO float voltage;                           // 用于存储计算后的电压值
uint32_t output_unix_timestamp;               // 用于存储计算后的时间戳

// 定义电压HEX缓冲区、时间戳HEX缓冲区和组合输出缓冲区
uint8_t voltage_hex[9] = {0};   // 电压HEX缓冲区
uint8_t timestamp_hex[9] = {0}; // 时间戳HEX缓冲区

// --- 初始化 (通常在 main 函数或外设初始化函数中调用一次) ---
void adc_dma_init(void)
{
    // 启动 ADC 并使能 DMA 传输
    // hadc1: ADC 句柄
    // (uint32_t*)adc_dma_buffer: DMA 目标缓冲区地址 (HAL库通常需要uint32_t*)
    // ADC_DMA_BUFFER_SIZE: 本次传输的数据量 (缓冲区大小)

    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_dma_buffer, ADC_DMA_BUFFER_SIZE);
}

// --- 处理任务 (在主循环或定时器回调中定期调用) ---
void ADC_Proc(void)
{
    uint32_t adc_sum = 0;

    // 1. 计算 DMA 缓冲区中所有采样值的总和
    //    注意：这里直接读取缓冲区，可能包含不同时刻的采样值
    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++)
    {
        adc_sum += adc_dma_buffer[i];
    }

    // 2. 计算平均 ADC 值
    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE;

    // 3. 将平均数字值转换为实际电压值
    voltage = ((float)adc_val * 3.3f) / 4096.0f; // 12位分辨率, 3.3V参考电压

    // 4. 使用计算出的平均值 (adc_val 或 voltage)
    // my_printf(&huart1, "Average ADC: %lu, Voltage: %.2fV\n", adc_val, voltage);
}

void ADC_TASK(void)
{
    // 如果LED_ADC_Mode为1且hide_config_step为0，则执行以下操作
    if ((Led_ADC_Mode >= 1) && (hide_config_step == 0))
    {
        // 打印当前日期和时间
        rs485_printf("%04d-%02d-%02d %02d:%02d:%02d ",
                  2000 + Date.Year, Date.Month, Date.Date, Time.Hours, Time.Minutes, Time.Seconds);

        // 如果电压值乘以Ratio小于limit，则打印电压值
        if (voltage * Ratio < limit)
        {
            rs485_printf("ch0 = %.2fV\r\n", voltage * Ratio);
            ucled[1] = 0;
        }
        // 否则，打印超阈值信息，并设置LED灯为1
        else
        {
            rs485_printf("OverLimit(%.2f)\r\n", limit);
            ucled[1] = 1;
        }
    }
    // 如果LED_ADC_Mode为1且hide_config_step为1，则执行以下操作
    else if ((Led_ADC_Mode >= 1) && (hide_config_step == 1))
    {
        uint8_t combined_output[17] = {0}; // 组合输出缓冲区

        // 1. 获取时间戳并转换为HEX
        output_unix_timestamp = rtc_to_unix_timestamp(NULL, &Date, &Time);
        timestamp_to_hex(output_unix_timestamp, timestamp_hex);

        // 2. 获取电压值并转换为HEX
        float actual_voltage = voltage * Ratio;
        voltage_to_hex(actual_voltage, voltage_hex);

        // 3. 组合时间戳和电压值
        memcpy(combined_output, timestamp_hex, 8);
        memcpy(combined_output + 8, voltage_hex, 8);
        combined_output[16] = '\0';

        // 4. 立即存储hide数据到TF卡
        SampleData_t sample_data;
        sample_data.timestamp = output_unix_timestamp;
        sample_data.voltage = actual_voltage;
        sample_data.is_overlimit = (actual_voltage >= limit) ? 1 : 0;

        // 存储hide数据到hideData文件夹
        SD_Save_HideData(&sample_data, combined_output, 16);

        // 如果超限，同时存储到overLimit文件夹
        if (sample_data.is_overlimit)
        {
            SD_Save_OverLimit_Data(&sample_data);
        }

        // 5. 根据阈值判断输出显示
        if (actual_voltage < limit)
        {
            rs485_printf("%s\r\n", combined_output);
            ucled[1] = 0;
        }
        else
        {
            rs485_printf("%s*\r\n", combined_output);
            ucled[1] = 1;
        }
    }
}

/**
 * @brief 将浮点电压值转换为8字符HEX格式
 * @param voltage 电压值
 * @param output 输出缓冲区（至少9字节）
 */
void voltage_to_hex(float voltage, uint8_t *output)
{
    // 1. 分离整数和小数部分
    uint16_t integer_part = (uint16_t)floorf(voltage);
    float fraction = voltage - integer_part;

    // 2. 将小数部分转换为16位定点数
    uint16_t fraction_part = (uint16_t)roundf(fraction * 65536.0f);

    // 3. 整数部分转4字符HEX
    snprintf((char *)output, 5, "%04X", integer_part);

    // 4. 小数部分转4字符HEX
    snprintf((char *)output + 4, 5, "%04X", fraction_part);
}

/**
 * @brief 数据存储任务 - 仅处理正常模式的数据存储
 * 注意：hide模式的数据存储已合并到ADC_TASK中
 */
void ADC_Data_Storage_Task(void)
{
    // 只有在ADC模式开启且为正常模式时才进行数据存储
    if ((Led_ADC_Mode >= 1) && (hide_config_step == 0))
    {
        // 准备数据结构
        SampleData_t sample_data;
        sample_data.timestamp = rtc_to_unix_timestamp(NULL, &Date, &Time);
        sample_data.voltage = voltage * Ratio;
        sample_data.is_overlimit = (sample_data.voltage >= limit) ? 1 : 0;

        // 正常模式：存储到sample文件夹
        if (sample_data.is_overlimit)
        {
            // 超限时同时存储到两个文件夹
            SD_Save_Sample_Data(&sample_data);
            SD_Save_OverLimit_Data(&sample_data);
        }
        else
        {
            // 正常时只存储到sample文件夹
            SD_Save_Sample_Data(&sample_data);
        }
    }
    // hide模式的数据存储已经在ADC_TASK中处理，这里不需要重复处理
}
