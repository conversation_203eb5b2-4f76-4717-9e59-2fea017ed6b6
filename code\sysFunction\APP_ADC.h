#ifndef __APP_ADC__H
#define __APP_ADC__H

#include "Mydefine.h"


void adc_dma_init(void);
void ADC_Proc(void);
void ADC_TASK(void);
void voltage_to_hex(float voltage, uint8_t *output); // 将电压值转换为十六进制数
void ADC_TASK_HIDE(void);
void ADC_Data_Storage_Task(void); // 数据存储任务

// 24位外置ADC函数声明
void ext_adc_init(void);                                                      // 初始化24位外置ADC
HAL_StatusTypeDef ext_adc_read_voltage_10v(float *voltage_out);              // 读取0-10V电压值
int32_t ext_adc_get_raw_value(void);                                         // 获取原始ADC值
HAL_StatusTypeDef ext_adc_read_voltage_averaged(uint8_t samples, float *voltage_out); // 多次采样平均
void EXT_ADC_TASK(void);                                                     // 24位ADC测量任务

extern __IO uint32_t adc_val;                        // 用于存储计算后的平均 ADC 值
extern __IO float voltage;                           // 用于存储计算后的电压值

#endif
