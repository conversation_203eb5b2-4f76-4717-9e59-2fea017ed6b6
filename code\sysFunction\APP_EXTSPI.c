/**
 * @file APP_EXTSPI.c
 * @brief ADS1220外置芯片电压测量函数 - 0-6V电压测量
 * <AUTHOR> Assistant
 * @date 2025
 */

#include "APP_EXTSPI.h"

static Measure_t Measure;

// 读取原始ADC数据
uint32_t Adc_Read_Voltage(void) {
    
    if(ADS1220_ReadData(&Measure.Voltage_Actual) != HAL_OK)
    {
        // 读取失败
        return 0;
    }
    // 读取成功
    
    return Measure.Voltage_Actual;

}

void ADC_READ_TEST(void)
{
    uint32_t Voltage = Adc_Read_Voltage();
    rs485_printf("Voltage: 0x%lx" ,Voltage);

}
