/**
 * @file APP_EXTSPI.h
 * @brief ADS1220外置芯片电压测量函数头文件 - 0-6V电压测量
 * <AUTHOR> Assistant
 * @date 2025
 */

#ifndef __APP_EXTSPI_H
#define __APP_EXTSPI_H

#ifdef __cplusplus
extern "C" {
#endif

#include "ADS1220.h"
#include "APP_Uart.h"

#define ADS1220_VREF      3.3f
#define ADS1220_MAX_VALUE 8388607.0f

typedef struct
{
    uint32_t Voltage_Actual;         //电压测量值
    uint32_t Current_Actual;         //电流测量值
    uint32_t Resistance_Actual;      //电阻测量值
    float Ture_Voltage;              //真实电压值
    float Ture_Current;              //真实电流值
    float True_resistance            //真实电阻值
}Measure_t;


void ADC_READ_TEST(void);

#ifdef __cplusplus
}
#endif

#endif /* __APP_EXTSPI_H */