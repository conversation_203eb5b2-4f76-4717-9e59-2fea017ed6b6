#ifndef __APP_FLASH_H
#define __APP_FLASH_H

#include "Mydefine.h"

// 数据存储相关定义
#define MAX_RECORDS_PER_FILE 10  // 每个文件最大记录数
#define POWER_ON_COUNT_ADDR 0x1000  // 上电次数存储地址

// Flash日志缓存相关定义
#define FLASH_LOG_CACHE_START_ADDR 0x2000    // Flash日志缓存起始地址
#define FLASH_LOG_CACHE_SIZE 0x8000          // Flash日志缓存大小 (32KB)
#define FLASH_LOG_CACHE_END_ADDR (FLASH_LOG_CACHE_START_ADDR + FLASH_LOG_CACHE_SIZE - 1)
#define FLASH_LOG_HEADER_ADDR 0x1800         // Flash日志头信息地址
#define MAX_LOG_ENTRY_SIZE 256               // 单条日志最大长度
#define FLASH_LOG_MAGIC 0x4C4F4721           // Flash日志魔数 "LOG!"

// 文件夹路径定义
#define SAMPLE_FOLDER "sample"
#define OVERLIMIT_FOLDER "overLimit"
#define LOG_FOLDER "log"
#define HIDEDATA_FOLDER "hideData"

// 数据存储结构体
typedef struct {
    uint32_t timestamp;     // Unix时间戳
    float voltage;          // 电压值
    uint8_t is_overlimit;   // 是否超限
} SampleData_t;

// Flash日志头信息结构体
typedef struct {
    uint32_t magic;           // 魔数，用于验证数据有效性
    uint32_t log_count;       // 日志条数
    uint32_t write_offset;    // 当前写入偏移量
    uint32_t total_size;      // 总的日志数据大小
    uint8_t is_transferred;   // 是否已转移到SD卡 (0:未转移, 1:已转移)
    uint8_t reserved[3];      // 保留字节，用于对齐
} FlashLogHeader_t;

// 文件管理结构体
typedef struct {
    uint8_t sample_count;      // 当前sample文件记录数
    uint8_t overlimit_count;   // 当前overlimit文件记录数
    uint8_t hidedata_count;    // 当前hidedata文件记录数
    uint32_t power_on_count;   // 上电次数
    char current_sample_file[32];    // 当前sample文件名
    char current_overlimit_file[32]; // 当前overlimit文件名
    char current_hidedata_file[32];  // 当前hidedata文件名
    char current_log_file[32];       // 当前log文件名
    uint8_t sd_available;      // SD卡是否可用 (0:不可用, 1:可用)
    uint8_t use_flash_cache;   // 是否使用Flash缓存 (0:不使用, 1:使用)
} FileManager_t;

void flash_task(void);
void test_sd_fatfs(void);
void flash_write(uint32_t addr, const char *data);
void flash_read(uint32_t addr, uint16_t len);
void SD_Task();
uint8_t SD_Test(void);
void SD_Read_conf_Task(void);

// 新增的数据存储函数
uint8_t SD_Init_Folders(void);
uint8_t SD_Save_Sample_Data(SampleData_t *data);
uint8_t SD_Save_OverLimit_Data(SampleData_t *data);
uint8_t SD_Save_HideData(SampleData_t *data, uint8_t *encrypted_data, uint16_t encrypted_len);
uint8_t SD_Write_Log(const char *log_message);
uint8_t SD_Write_Log_With_Timestamp(const char *log_message);
void SD_Init_Log_File(void);

// 日志写入函数（仅SD卡）
uint8_t Write_Log_Universal(const char *log_message);
uint8_t Write_Log_With_Timestamp_Universal(const char *log_message);
uint32_t SD_Get_Power_On_Count(void);
void SD_Increment_Power_On_Count(void);
void SD_Reset_Power_On_Count(void);
void SD_Generate_DateTime_String(uint32_t timestamp, char *datetime_str);
uint8_t SD_Create_New_Sample_File(void);
uint8_t SD_Create_New_OverLimit_File(void);
uint8_t SD_Create_New_HideData_File(void);
// void Simple_Encrypt(uint8_t *data, uint16_t len, uint8_t key, uint8_t *encrypted_data);
uint32_t SD_Get_Card_Memory_KB(void);

extern uint8_t read_buffer[256]; // 假设页面大小为 256 字节，需根据实际定义
extern FileManager_t file_manager; // 文件管理器全局变量

#endif // __FLASH_APP_H
