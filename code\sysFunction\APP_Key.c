#include "APP_Key.h"

uint8_t Key_Val = 0;  // 当前按键值 (哪个键按下? 0代表无)
uint8_t Key_Old = 0;  // 上一次循环检测到的按键值
uint8_t Key_Down = 0; // 按键按下的标志 (哪个键刚被按下?)
uint8_t Key_Up = 0;   // 按键释放的标志 (哪个键刚被释放?)

uint8_t Key1_Mode = 0; // KEY1 模式 

uint8_t Key_Read(void)
{
    uint8_t key_value = 0; // 初始化为0 (无按键)
    // 假设 KEY1-6 在 GPIOE 的第 7-12 引脚
    if (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7) == GPIO_PIN_RESET)
        key_value = 1; // KEY1
    if (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_8) == GPIO_PIN_RESET)
        key_value = 2; // KEY2
    if (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9) == GPIO_PIN_RESET)
        key_value = 3; // KEY3
    if (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_10) == GPIO_PIN_RESET)
        key_value = 4; // KEY4
    if (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11) == GPIO_PIN_RESET)
        key_value = 5; // KEY5
    if (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_12) == GPIO_PIN_RESET)
        key_value = 6; // KEY6

    return key_value; // 返回当前按下的按键编号 (1-6)
}

void Key_Proc()
{
    Key_Val = Key_Read(); // 读取当前按键值
    Key_Down = Key_Val & (Key_Old ^ Key_Val);
    Key_Up = ~Key_Val & (Key_Val ^ Key_Old);
    Key_Old = Key_Val;

    switch (Key_Down)
    {
    case 1: // KEY1 按下
        OLED_Init();
        Led_ADC_Mode++; // 切换模式
        if(Led_ADC_Mode == 1)
        {
            rs485_printf( "Periodic Sampling\r\n");

            // 显示当前设置的采样周期
            if (sample_cycle == 5000) {
                rs485_printf("sample cycle: 5s\r\n");
            } else if (sample_cycle == 10000) {
                rs485_printf("sample cycle: 10s\r\n");
            } else if (sample_cycle == 15000) {
                rs485_printf("sample cycle: 15s\r\n");
            } else {
                rs485_printf("sample cycle: 5s\r\n");
            }

            // 记录采样开始日志
            char log_msg[64];
            uint32_t cycle_seconds = sample_cycle / 1000;
            snprintf(log_msg, sizeof(log_msg), "sample start - cycle %lus (key press)", cycle_seconds);
            Write_Log_With_Timestamp_Universal(log_msg);
        }
        if(Led_ADC_Mode % 2 == 0)
        {
            Update_Sample_Cycle(10); // 设置采样周期为快速模式（用于OLED刷新）
            rs485_printf("Periodic Sampling STOP\r\n");
            Led_ADC_Mode = 0;

            // 记录采样停止日志
            Write_Log_With_Timestamp_Universal("sample stop (key press)");
        }
        break;
    case 2: // KEY2 按下
        Update_Sample_Cycle(5000);
        rs485_printf("sample cycle adjust: 5s\r\n");

        // 记录周期切换日志
        Write_Log_With_Timestamp_Universal("cycle switch to 5s (key press)");
        break;
    case 3: // KEY3 按下
        Update_Sample_Cycle(10000);
        rs485_printf("sample cycle adjust: 10s\r\n");

        // 记录周期切换日志
        Write_Log_With_Timestamp_Universal("cycle switch to 10s (key press)");
        break;
    case 4: // KEY4 按下
        Update_Sample_Cycle(15000);
        rs485_printf("sample cycle adjust: 15s\r\n");

        // 记录周期切换日志
        Write_Log_With_Timestamp_Universal("cycle switch to 15s (key press)");
        break;
    case 5: // KEY5 按下

        break;
    case 6: // KEY6 按下

        break;
    }
}
