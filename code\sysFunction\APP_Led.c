#include "APP_Led.h"
#include "math.h"

uint8_t ucled[6] = {0, 0, 0, 0, 0, 0};

void Led_Disp(uint8_t *ucLed)
{
    uint8_t temp = 0x00;            // 用于记录当前 LED 状态的临时变量 (最低6位有效)
    static uint8_t temp_old = 0xff; // 记录之前 LED 状态的变量, 用于判断是否需要更新显示

    for (int i = 0; i < 6; i++) // 遍历6个LED的状态
    {
        // 将LED状态整合到temp变量中，方便后续比较
        if (ucLed[i])
            temp |= (1 << i); // 如果ucLed[i]为1, 则将temp的第i位置1
    }

    // 仅当当前状态与之前状态不同的时候，才更新显示
    if (temp != temp_old)
    {
        // 使用HAL库函数根据temp的值设置对应引脚状态 (假设高电平点亮)
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8, (temp & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);  // LED 1
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_9, (temp & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);  // LED 2
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_10, (temp & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 3
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_11, (temp & 0x08) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 4
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_12, (temp & 0x10) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 5
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_13, (temp & 0x20) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 6

        temp_old = temp; // 更新记录的旧状态
    }
}

/**
 * @brief LED 显示处理函数 - 呼吸灯效果 (在主循环中周期性调用)
 */
void Led_Proc(void)
{
    // 调用之前定义的 led_disp 函数，将计算好的 ucLed 状态更新到实际的GPIO引脚
    Led_Disp(ucled); // 注意：led_disp内部最好也有优化，避免状态不变时重复写GPIO
}

void LED_SHINE(void)
{
    if (Led_ADC_Mode == 1)
        ucled[0] ^= 1;
    if(Led_ADC_Mode == 0)
        ucled[0] = 0;
}
