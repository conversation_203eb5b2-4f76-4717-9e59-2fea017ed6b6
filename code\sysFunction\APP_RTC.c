#include "APP_RTC.h"

RTC_TimeTypeDef Time;
RTC_DateTypeDef Date;

void RTC_Task(void)
{
    /*
    一定要时间和日期一起设置，否则会导致一些bug
    导致时间不能递增
    */
    HAL_RTC_GetTime(&hrtc, &Time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &Date, RTC_FORMAT_BIN);
    
}
// 月份天数表（非闰年）
static const uint8_t days_in_month[12] = {
    31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31
};

/**
 * @brief 检查是否为闰年
 * @param year 年份（完整年份，如2023）
 * @return 1表示闰年，0表示平年
 */
static uint8_t is_leap_year(uint16_t year)
{
    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
        return 1;
    }
    return 0;
}


// 解析RTC时间字符串的辅助函数
HAL_StatusTypeDef parse_rtc_datetime_string(const char *datetime_str, RTC_TimeTypeDef *time, RTC_DateTypeDef *date)
{
    // 验证输入参数
    if (!datetime_str || !time || !date) {
        return HAL_ERROR;
    }

    // 查找等号位置
    const char *equal_pos = strchr(datetime_str, '=');
    if (!equal_pos) {
        return HAL_ERROR;
    }

    // 跳过等号和空格，找到日期时间字符串开始位置
    const char *dt_start = equal_pos + 1;
    while (*dt_start == ' ') dt_start++; // 跳过空格

    // 验证格式：YYYY/M/D HH:MM:SS 或 YYYY/MM/DD HH:MM:SS
    int year, month, day, hour, minute, second;
    int parsed = sscanf(dt_start, "%d/%d/%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);

    // 验证范围
    if (year < 2000 || year > 2099 || month < 1 || month > 12 ||
        day < 1 || day > 31 || hour < 0 || hour > 23 ||
        minute < 0 || minute > 59 || second < 0 || second > 59) {
        rs485_printf("RTC时间参数超出范围\r\n");
        return HAL_ERROR;
    }

    // 设置日期
    date->Year = year - 2000;  // RTC年份从2000年开始计算
    date->Month = month;
    date->Date = day;
    date->WeekDay = RTC_WEEKDAY_MONDAY; // 可以计算实际星期，这里简化处理

    // 设置时间
    time->Hours = hour;
    time->Minutes = minute;
    time->Seconds = second;
    time->TimeFormat = RTC_HOURFORMAT_24;
    time->DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
    time->StoreOperation = RTC_STOREOPERATION_RESET;

    return HAL_OK;
}


/**
 * @brief 将日期时间转换为Unix时间戳
 * @param year 完整年份（如2025）
 * @param month 月份（1-12）
 * @param day 日期（1-31）
 * @param hour 小时（0-23）
 * @param min 分钟（0-59）
 * @param sec 秒（0-59）
 * @return Unix时间戳（秒）
 */
static uint32_t datetime_to_timestamp(uint16_t year, uint8_t month, uint8_t day,
                                      uint8_t hour, uint8_t min, uint8_t sec)
{
    uint32_t total_days = 0;
    
    // 计算1970年到目标年份的天数
    for (uint16_t y = 1970; y < year; y++) {
        total_days += 365;
        if (is_leap_year(y)) {
            total_days++;
        }
    }
    
    // 计算目标年份中到目标月份的天数
    for (uint8_t m = 1; m < month; m++) {
        total_days += days_in_month[m - 1];
        
        // 闰年二月调整
        if (m == 2 && is_leap_year(year)) {
            total_days++;
        }
    }
    
    // 加上当月天数（减去1，因为天数从1开始）
    total_days += day - 1;
    
    // 计算总秒数
    uint32_t total_seconds = total_days * 86400UL;
    total_seconds += hour * 3600UL;
    total_seconds += min * 60UL;
    total_seconds += sec;
    
    return total_seconds;
}

/**
 * @brief 将时间戳转换为8字符HEX格式
 * @param timestamp Unix时间戳
 * @param output 输出缓冲区（至少9字节）
 */
void timestamp_to_hex(uint32_t timestamp, uint8_t *output)
{
    // 大端序转换
    for (int i = 0; i < 4; i++) {
        uint8_t byte = (timestamp >> (24 - i * 8)) & 0xFF;
        snprintf((char *)output + i * 2, 3, "%02X", byte);
    }
    output[8] = '\0';
}

/**
 * @brief 将 RTC 日期和时间转换为 Unix 时间戳
 * @param hrtc RTC 句柄指针
 * @param date RTC 日期结构体
 * @param time RTC 时间结构体
 * @return Unix 时间戳（秒）
 */
uint32_t rtc_to_unix_timestamp(RTC_HandleTypeDef *hrtc, 
                              RTC_DateTypeDef *date, 
                              RTC_TimeTypeDef *time)
{
    // 从RTC获取日期和时间（如果指针为空）
    if (date == NULL || time == NULL) {
        RTC_DateTypeDef local_date;
        RTC_TimeTypeDef local_time;
        
        HAL_RTC_GetTime(hrtc, &local_time, RTC_FORMAT_BIN);
        HAL_RTC_GetDate(hrtc, &local_date, RTC_FORMAT_BIN);
        
        date = &local_date;
        time = &local_time;
    }
    
    // 计算完整年份（RTC年份是2000年以来的偏移量）
    uint16_t full_year = 2000 + date->Year;
    
    // 转换为Unix时间戳
    uint32_t timestamp = datetime_to_timestamp(full_year, date->Month, date->Date,
                                time->Hours, time->Minutes, time->Seconds);
    // 减去时区偏移（转换为UTC时间）
    return timestamp - (TIMEZONE_OFFSET * 3600);
}

