#ifndef __APP_RTC__H
#define __APP_RTC__H

#define TIMEZONE_OFFSET 8 // 东八区（北京时间）

#include "MyDefine.h"
void RTC_Task(void);
uint32_t rtc_to_unix_timestamp(RTC_HandleTypeDef *hrtc, 
                              RTC_DateTypeDef *date, 
                              RTC_TimeTypeDef *time);
void timestamp_to_hex(uint32_t timestamp, uint8_t *output);
HAL_StatusTypeDef parse_rtc_datetime_string(const char *datetime_str, RTC_TimeTypeDef *time, RTC_DateTypeDef *date);

extern RTC_TimeTypeDef Time;
extern RTC_DateTypeDef Date;

#endif
