#include "APP_Uart.h"

uint16_t uart2_rx_index = 0;
uint32_t uart2_rx_ticks = 0;
uint8_t uart2_rx_buffer[128] = {0};		// 接收缓冲区
uint8_t uart2_rx_dma_buffer[128] = {0}; // 接收缓冲区
uint8_t uart2_dma_buffer[128] = {0};	// 接收缓冲区
uint8_t uart2_flag = 0;

double Ratio = 1.0;	 // 变比
double Ratio_Old;	 // 保存上一次的值
double limit = 10.0; // 阈值
double limit_Old;	 // 保存上一次的值

uint8_t rtc_receive_data[6] = {0};

struct rt_ringbuffer uart2_ringbuffer;
uint8_t uart2_ringbuffer_pool[128];

uint8_t rtc_config_step = 0;   // rtc配置模式
uint8_t ratio_config_step = 0; // ratio配置模式
uint8_t limit_config_step = 0; // ratio配置模式
uint8_t hide_config_step = 0;  // hide配置模式

uint8_t Led_ADC_Mode = 0; // 0:关闭 1:启动

// 新增：RS485 printf函数 - 增强版本
int rs485_printf(const char *format, ...)
{
	char buffer[512]; // 临时存储格式化后的字符串
	va_list arg;	  // 处理可变参数
	int len;		  // 最终字符串长度

	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	// 通过RS485发送数据
	RS485_Transmit((uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

// 新增：RS485专用发送函数
HAL_StatusTypeDef RS485_Transmit(uint8_t *data, uint16_t size, uint32_t timeout)
{
	HAL_StatusTypeDef status;

	RS485_TX_MODE(); // 切换到发送模式
	HAL_Delay(1);	 // 短暂延时确保模式切换完成

	status = HAL_UART_Transmit(&huart2, data, size, timeout);

	HAL_Delay(1);	 // 等待发送完成
	RS485_RX_MODE(); // 切换回接收模式

	return status;
}

// 超时解析
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	if (huart->Instance == USART2)
	{
		// 2. 更新收货时间：记录下当前时间
		uart2_rx_ticks = uwTick;
		// 3. 货物入库：将收到的字节放入缓冲区（HAL库已自动完成）
		//    并增加计数器
		//    (注意：实际入库由 HAL_UART_Receive_IT 触发，这里只更新计数)
		uart2_rx_index++;
		// 4. 准备下次收货：再次告诉硬件，我还想收一个字节
		HAL_UART_Receive_IT(&huart2, &uart2_rx_buffer[uart2_rx_index], 1);
	}
}

/**
 * @brief UART DMA接收完成或空闲事件回调函数
 * @param huart UART句柄
 * @param Size 指示在事件发生前，DMA已经成功接收了多少字节的数据
 * @retval None
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	if (huart->Instance == USART2)
	{
		// 2. 紧急停止当前的 DMA 传输 (如果还在进行中)
		//    因为空闲中断意味着发送方已经停止，防止 DMA 继续等待或出错
		HAL_UART_DMAStop(huart);

		rt_ringbuffer_put(&uart2_ringbuffer, uart2_rx_dma_buffer, Size);

		// 5. 清空 DMA 接收缓冲区，为下次接收做准备
		//    虽然 memcpy 只复制了 Size 个，但清空整个缓冲区更保险
		memset(uart2_rx_dma_buffer, 0, sizeof(uart2_rx_dma_buffer));

		// 6. **关键：重新启动下一次 DMA 空闲接收**
		//    必须再次调用，否则只会接收这一次
		if (HAL_UARTEx_ReceiveToIdle_DMA(&huart2, uart2_rx_dma_buffer, sizeof(uart2_rx_dma_buffer)) != HAL_OK)
		{
			// DMA重启失败，记录错误但不停止系统
			rs485_printf("UART2 DMA restart failed\r\n");
		}

		// 7. 如果之前关闭了半满中断，可能需要在这里再次关闭 (根据需要)
		__HAL_DMA_DISABLE_IT(&hdma_usart2_rx, DMA_IT_HT);
	}
}

// 新增：UART发送完成回调
void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
{
	if (huart->Instance == USART2)
	{
		// RS485发送完成，切换回接收模式
		HAL_Delay(1);
		RS485_RX_MODE();
	}
}

void save_config_to_flash(void)
{
	char buffer[64];

	// 格式化配置数据
	snprintf(buffer, sizeof(buffer), "RATIO:%.2f,LIMIT:%.2f", Ratio, limit);

	// 写入Flash
	flash_write(CONFIG_ADDRESS, buffer);

	rs485_printf("Config saved: Ratio=%.2f\r\n Limit=%.2f\r\n", Ratio, limit);
}

// 从Flash读取配置
void read_config_from_flash(void)
{
	char buffer[64];
	float new_ratio, new_limit;

	// 从Flash读取
	flash_read(CONFIG_ADDRESS, sizeof(buffer));

	// 确保字符串终止
	if (read_buffer[sizeof(read_buffer) - 1] != '\0')
	{
		read_buffer[sizeof(read_buffer) - 1] = '\0';
	}

	// 解析配置
	if (sscanf((char *)read_buffer, "RATIO:%f,LIMIT:%f", &new_ratio, &new_limit) == 2)
	{
		// 验证范围
		if (new_ratio >= 0.0f && new_ratio <= 100.0f)
		{
			Ratio = new_ratio;
			rs485_printf("Ratio: %.2f\r\n", Ratio);
		}
		if (new_limit >= 0.0f && new_limit <= 200.0f)
		{
			limit = new_limit;
			rs485_printf("Limit: %.2f\r\n", limit);
		}
	}
}

// 检测状态机：buffer的输入内容
void parse_uart_command(uint8_t *buffer, uint16_t length)
{
	// 确保缓冲区末尾有字符串结束符
	if (length < sizeof(uart2_dma_buffer))
		buffer[length] = '\0';
	else
		buffer[sizeof(uart2_dma_buffer) - 1] = '\0';

	// 查询指令解析
	// 自检
	if (strncmp((char *)buffer, "test", 4) == 0)
	{
		HAL_RTC_GetTime(&hrtc, &Time, RTC_FORMAT_BIN);
		HAL_RTC_GetDate(&hrtc, &Date, RTC_FORMAT_BIN);

		// 记录硬件测试开始日志
		Write_Log_With_Timestamp_Universal("system hardware test");

		system_selftest();

		// 记录测试结果日志
		Write_Log_With_Timestamp_Universal("test ok");
	}
	// RTC时间配置
	else if (strncmp((char *)buffer, "RTC Config", 10) == 0)
	{
		rs485_printf("Input Datetime\r\n");
		rtc_config_step = 1; // 进入配置模式

		// 记录RTC配置开始日志
		Write_Log_With_Timestamp_Universal("rtc config");
	}
	else if (rtc_config_step == 1)
	{
		// 验证输入格式：YYYY-MM-DD HH:MM:SS
		if (length >= 19 && buffer[4] == '-' && buffer[7] == '-' && buffer[10] == ' ' &&
			buffer[13] == ':' && buffer[16] == ':')
		{
			RTC_TimeTypeDef time = {0};
			RTC_DateTypeDef date = {0};

			// 年份
			date.Year = (buffer[2] - '0') * 10 + (buffer[3] - '0');

			// 月份
			date.Month = (buffer[5] - '0') * 10 + (buffer[6] - '0');

			// 日期
			date.Date = (buffer[8] - '0') * 10 + (buffer[9] - '0');

			// 小时
			time.Hours = (buffer[11] - '0') * 10 + (buffer[12] - '0');

			// 分钟
			time.Minutes = (buffer[14] - '0') * 10 + (buffer[15] - '0');

			// 秒
			time.Seconds = (buffer[17] - '0') * 10 + (buffer[18] - '0');

			// 设置时间格式
			time.TimeFormat = RTC_HOURFORMAT_24;
			time.DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
			time.StoreOperation = RTC_STOREOPERATION_RESET;

			// 设置日期
			if (HAL_RTC_SetDate(&hrtc, &date, RTC_FORMAT_BIN) != HAL_OK)
			{
				rs485_printf("日期设置失败!\r\n");
			}

			// 设置时间
			if (HAL_RTC_SetTime(&hrtc, &time, RTC_FORMAT_BIN) != HAL_OK)
			{
				rs485_printf("时间设置失败!\r\n");
			}

			// 验证设置
			HAL_RTC_GetTime(&hrtc, &Time, RTC_FORMAT_BIN);
			HAL_RTC_GetDate(&hrtc, &Date, RTC_FORMAT_BIN);
			rs485_printf("RTC Config Scuess\r\n");
			rs485_printf("Time: %04d-%02d-%02d %02d:%02d:%02d\r\n", 2000 + Date.Year, Date.Month, Date.Date,
						 Time.Hours, Time.Minutes, Time.Seconds);
			rtc_config_step = 0; // 退出配置模式

			// 记录RTC配置成功日志
			char log_msg[128];
			snprintf(log_msg, sizeof(log_msg), "rtc config success to %04d-%02d-%02d %02d:%02d:%02d",
					 2000 + Date.Year, Date.Month, Date.Date, Time.Hours, Time.Minutes, Time.Seconds);
			Write_Log_With_Timestamp_Universal(log_msg);
		}
	}
	// RTC时间查询
	else if (strncmp((char *)buffer, "RTC now", 7) == 0)
	{
		rs485_printf("Current time: %04d-%02d-%02d %02d:%02d:%02d\r\n", 2000 + Date.Year, Date.Month, Date.Date,
					 Time.Hours, Time.Minutes, Time.Seconds);
	}
	// 变比设置
	else if (strncmp((char *)buffer, "ratio", 5) == 0)
	{
		rs485_printf("Ratio = %.1f\r\n", Ratio);
		rs485_printf("Input value(0~100) :\r\n");
		ratio_config_step = 1; // 进入配置模式

		// 记录比例配置开始日志
		Write_Log_With_Timestamp_Universal("ratio config");
	}
	else if (ratio_config_step == 1)
	{

		// 检查输入格式是否有效
		uint8_t valid_format_ratio = 1;
		uint8_t decimal_found_ratio = 0;
		uint8_t decimal_position_ratio = 0;

		// 检查每个字符是否合法
		for (uint16_t i = 0; i < length; i++)
		{
			if (buffer[i] == '.')
			{
				if (decimal_found_ratio)
				{ // 多个小数点
					valid_format_ratio = 0;
					break;
				}
				decimal_found_ratio = 1;
				decimal_position_ratio = i;
			}
			else if (!IS_DIGIT(buffer[i]))
			{
				valid_format_ratio = 0;
				break;
			}
		}

		// 必须有小数点且不在开头或结尾
		if (!decimal_found_ratio || decimal_position_ratio == 0 || decimal_position_ratio == length - 1)
		{
			valid_format_ratio = 0;
		}

		if (valid_format_ratio)
		{
			// 解析整数部分
			float integer_part = 0;
			for (uint8_t i = 0; i < decimal_position_ratio; i++)
			{
				integer_part = integer_part * 10 + (buffer[i] - '0');
			}

			// 解析小数部分
			float decimal_part = 0;
			float divisor = 10.0f; // 从十分位开始
			for (uint8_t i = decimal_position_ratio + 1; i < length; i++)
			{
				decimal_part += (buffer[i] - '0') / divisor;
				divisor *= 10.0f; // 移动到下一位
			}

			// 计算最终值
			float new_ratio = integer_part + decimal_part;

			// 检查范围
			if (new_ratio >= 0.0f && new_ratio <= 100.0f)
			{
				Ratio = new_ratio;
				Ratio_Old = Ratio;
				rs485_printf("ratio modified success\r\n");
				rs485_printf("Ratio = %.1f\r\n", Ratio);

				// 记录比例配置成功日志
				char log_msg[64];
				snprintf(log_msg, sizeof(log_msg), "ratio config success to %.2f", Ratio);
				Write_Log_With_Timestamp_Universal(log_msg);
			}
			else
			{
				rs485_printf("ratio invalid\r\n");
				rs485_printf("Ratio = %.1f\r\n", Ratio);

				// 记录操作日志
				char log_msg[64];
				snprintf(log_msg, sizeof(log_msg), "Ratio invalid: %.1f (out of range)\r\n", new_ratio);
				SD_Write_Log(log_msg);
			}
			ratio_config_step = 0; // 退出配置模式
		}
	}
	else if (strncmp((char *)buffer, "start", 5) == 0)
	{
		OLED_Init();
		Led_ADC_Mode = 1;
		rs485_printf("Periodic Sampling\r\n");

		// 显示当前设置的采样周期
		if (sample_cycle == 5000)
		{
			rs485_printf("sample cycle: 5s\r\n");
		}
		else if (sample_cycle == 10000)
		{
			rs485_printf("sample cycle: 10s\r\n");
		}
		else if (sample_cycle == 15000)
		{
			rs485_printf("sample cycle: 15s\r\n");
		}

		// 记录采样开始日志
		char log_msg[64];
		uint32_t cycle_seconds = sample_cycle / 1000;
		snprintf(log_msg, sizeof(log_msg), "sample start - cycle %lus (command)", cycle_seconds);
		Write_Log_With_Timestamp_Universal(log_msg);
	}
	else if (strncmp((char *)buffer, "stop", 4) == 0)
	{
		OLED_Init();
		Led_ADC_Mode = 0;
		rs485_printf("Periodic Sampling STOP\r\n");

		// 记录采样停止日志
		Write_Log_With_Timestamp_Universal("sample stop (command)");
	}
	// 阈值设置
	else if (strncmp((char *)buffer, "limit", 5) == 0)
	{
		rs485_printf("Limit = %.1f\r\n", limit);
		rs485_printf("Input value(0~200) :\r\n");
		limit_config_step = 1; // 进入配置模式

		// 记录限制配置开始日志
		Write_Log_With_Timestamp_Universal("limit config");
	}
	else if (limit_config_step == 1)
	{
		// 检查输入格式是否有效 - 只接受整数
		uint8_t valid_format_limit = 1;

		// 检查每个字符是否为数字
		for (uint16_t i = 0; i < length; i++)
		{
			if (!IS_DIGIT(buffer[i]))
			{
				valid_format_limit = 0;
				break;
			}
		}

		if (valid_format_limit)
		{
			// 解析整数值
			int new_limit = 0;
			for (uint8_t i = 0; i < length; i++)
			{
				new_limit = new_limit * 10 + (buffer[i] - '0');
			}

			// 检查范围
			if (new_limit >= 0 && new_limit <= 200)
			{
				limit = (float)new_limit;
				rs485_printf("limit modified success\r\n");
				rs485_printf("Limit = %.1f\r\n", limit);

				// 记录限制配置成功日志
				char log_msg[64];
				snprintf(log_msg, sizeof(log_msg), "limit config success to %.2f", limit);
				Write_Log_With_Timestamp_Universal(log_msg);
			}
			else
			{
				rs485_printf("limit invalid\r\n");
				rs485_printf("Limit = %.1f\r\n", limit);

				// 记录操作日志
				char log_msg[64];
				snprintf(log_msg, sizeof(log_msg), "Limit invalid: %d (out of range)\r\n", new_limit);
				SD_Write_Log(log_msg);
			}
			limit_config_step = 0; // 退出配置模式
		}
	}
	else if (strncmp((char *)buffer, "hide", 4) == 0)
	{
		hide_config_step = 1;
		rs485_printf("Hide mode enabled\r\n");

		// 记录隐藏数据日志
		Write_Log_With_Timestamp_Universal("hide data");
	}
	else if (strncmp((char *)buffer, "unhide", 6) == 0)
	{
		hide_config_step = 0;
		rs485_printf("Hide mode disabled\r\n");

		// 记录取消隐藏数据日志
		Write_Log_With_Timestamp_Universal("unhide data");
	}

	else if (strncmp((char *)buffer, "config save", 11) == 0)
	{
		save_config_to_flash();
		rs485_printf("\r\n");
		rs485_printf(" save paramaters to flash\r\n");
		// 记录操作日志
		SD_Write_Log("Command: config save - Configuration saved to flash\r\n");
	}
	else if (strncmp((char *)buffer, "config read", 11) == 0)
	{
		read_config_from_flash();

		// 记录操作日志
		SD_Write_Log("Command: config read - Configuration read from flash\r\n");
	}
	else if (strncmp((char *)buffer, "conf", 4) == 0)
	{
		// 读取文件信息
		SD_Read_conf_Task();
	}
	else if (strncmp((char *)buffer, "sd init", 7) == 0)
	{
		// 初始化SD卡文件夹
		if (SD_Init_Folders() == 0)
		{
			rs485_printf("SD folders initialized successfully\r\n");
		}
		else
		{
			rs485_printf("SD folders initialization failed\r\n");
		}
	}
	else if (strncmp((char *)buffer, "sd test", 7) == 0)
	{
		// 测试数据存储
		SampleData_t test_data;
		test_data.timestamp = rtc_to_unix_timestamp(&hrtc, &Date, &Time);
		test_data.voltage = 2.5f;
		test_data.is_overlimit = 0;

		if (SD_Save_Sample_Data(&test_data) == 0)
		{
			rs485_printf("Test sample data saved\r\n");
		}
		else
		{
			rs485_printf("Failed to save test sample data\r\n");
		}
		// 测试超限数据
		test_data.voltage = 5.0f;
		test_data.is_overlimit = 1;
		if (SD_Save_OverLimit_Data(&test_data) == 0)
		{
			rs485_printf("Test overlimit data saved\r\n");
		}
		else
		{
			rs485_printf("Failed to save test overlimit data\r\n");
		}

		// 测试日志
		SD_Write_Log("Test log message\r\n");
		rs485_printf("Test log written\r\n");
	}
	else if (strncmp((char *)buffer, "reset count", 11) == 0)
	{
		// 重置上电次数（用于测试）
		SD_Reset_Power_On_Count();
	}
	else if (strncmp((char *)buffer, "reset log", 9) == 0)
	{
		// 重置上电次数的别名命令
		SD_Reset_Power_On_Count();
	}
	else if (strncmp((char *)buffer, "help", 4) == 0)
	{
		// 显示帮助信息
		rs485_printf("=== Available Commands ===\r\n");
		rs485_printf("test          - System self-test\r\n");
		rs485_printf("RTC Config    - Configure RTC time\r\n");
		rs485_printf("RTC now       - Show current time\r\n");
		rs485_printf("ratio         - Set voltage ratio (0-100)\r\n");
		rs485_printf("limit         - Set alarm limit (0-200)\r\n");
		rs485_printf("start         - Start sampling\r\n");
		rs485_printf("stop          - Stop sampling\r\n");
		rs485_printf("hide          - Enable encryption mode\r\n");
		rs485_printf("unhide        - Disable encryption mode\r\n");
		rs485_printf("config save   - Save config to flash\r\n");
		rs485_printf("config read   - Read config from flash\r\n");
		rs485_printf("conf          - Read config.ini file\r\n");
		rs485_printf("reset count   - Reset power on counter\r\n");
		rs485_printf("reset log     - Reset log counter\r\n");
		rs485_printf("sd init       - Initialize SD folders\r\n");
		rs485_printf("sd test       - Test SD card functions\r\n");
		rs485_printf("help          - Show this help\r\n");
		rs485_printf("========================\r\n");
	}
}

void prase_rs485_command(uint8_t *buffer, uint16_t length)
{
	// 确保缓冲区末尾有字符串结束符
	if (length < sizeof(uart2_dma_buffer))
		buffer[length] = '\0';
	else
		buffer[sizeof(uart2_dma_buffer) - 1] = '\0';

	// 解析命令
	if (strncmp((char *)buffer, "command:deviceID_read", 21) == 0)
	{
		uint32_t flash_jedec_id = spi_flash_read_id(); // JEDEC ID
		rs485_printf("0x%06lX\r\n", flash_jedec_id);
	}
	else if (strncmp((char *)buffer, "command:config_rtc", 18) == 0)
	{
		RTC_TimeTypeDef time = {0};
		RTC_DateTypeDef date = {0};

		// 使用新的解析函数
		if (parse_rtc_datetime_string((char *)buffer, &time, &date) == HAL_OK)
		{
			// 设置日期
			if (HAL_RTC_SetDate(&hrtc, &date, RTC_FORMAT_BIN) != HAL_OK)
			{
				rs485_printf("日期设置失败!\r\n");
				return;
			}

			// 设置时间
			if (HAL_RTC_SetTime(&hrtc, &time, RTC_FORMAT_BIN) != HAL_OK)
			{
				rs485_printf("时间设置失败!\r\n");
				return;
			}

			// 验证设置 - 重新读取确保设置成功
			HAL_RTC_GetTime(&hrtc, &Time, RTC_FORMAT_BIN);
			HAL_RTC_GetDate(&hrtc, &Date, RTC_FORMAT_BIN);

			rs485_printf("RTC Config Success\r\n");
			rs485_printf("Time: %04d-%02d-%02d %02d:%02d:%02d\r\n",
						2000 + Date.Year, Date.Month, Date.Date,
						Time.Hours, Time.Minutes, Time.Seconds);

			// 记录RTC配置成功日志
			char log_msg[128];
			snprintf(log_msg, sizeof(log_msg), "rtc config success to %04d-%02d-%02d %02d:%02d:%02d",
					2000 + Date.Year, Date.Month, Date.Date, Time.Hours, Time.Minutes, Time.Seconds);
			Write_Log_With_Timestamp_Universal(log_msg);
		}
		else
		{
			rs485_printf("RTC配置失败: 格式错误\r\n");
			rs485_printf("正确格式: command:config_rtc = YYYY/M/D HH:MM:SS\r\n");
			rs485_printf("示例: command:config_rtc = 2025/8/2 21:06:44\r\n");
		}
	}
	else if (strncmp((char *)buffer, "command:ads1220_test", 20) == 0)
	{
		// 使用新驱动初始化ADS1220
		rs485_printf("使用新驱动初始化ADS1220...\r\n");
		if (ADS1220_Init(&hspi3) == HAL_OK) {
			rs485_printf("ADS1220新驱动初始化成功\r\n");
		} else {
			rs485_printf("ADS1220新驱动初始化失败\r\n");
		}
	}
	else if (strncmp((char *)buffer, "command:ads1220_config_check", 29) == 0)
	{
		// 验证ADS1220配置是否与目标一致
		rs485_printf("=== ADS1220配置验证 ===\r\n");

		uint8_t reg_data[4];
		if (ADS1220_ReadRegister(0x00, 4, reg_data) == HAL_OK) {
			rs485_printf("当前寄存器配置: 0x%02X 0x%02X 0x%02X 0x%02X\r\n",
						reg_data[0], reg_data[1], reg_data[2], reg_data[3]);

			// 解析寄存器0 (CONFIG0)
			uint8_t mux = (reg_data[0] & 0xF0) >> 4;
			uint8_t gain = (reg_data[0] & 0x0E) >> 1;
			uint8_t pga_bypass = reg_data[0] & 0x01;

			rs485_printf("寄存器0解析:\r\n");
			rs485_printf("  MUX设置: 0x%X ", mux);
			switch(mux) {
				case 0x6: rs485_printf("(AIN0单端)\r\n"); break;
				case 0x7: rs485_printf("(AIN1单端)\r\n"); break;
				case 0x8: rs485_printf("(AIN2单端)\r\n"); break;
				case 0x9: rs485_printf("(AIN3单端)\r\n"); break;
				default: rs485_printf("(其他模式)\r\n"); break;
			}
			rs485_printf("  增益: %d倍\r\n", 1 << gain);
			rs485_printf("  PGA: %s\r\n", pga_bypass ? "旁路" : "启用");

			// 解析寄存器1 (CONFIG1)
			uint8_t data_rate = (reg_data[1] & 0xE0) >> 5;
			uint8_t op_mode = (reg_data[1] & 0x18) >> 3;
			uint8_t conv_mode = (reg_data[1] & 0x04) >> 2;

			rs485_printf("寄存器1解析:\r\n");
			rs485_printf("  数据速率: ");
			switch(data_rate) {
				case 0: rs485_printf("20 SPS\r\n"); break;
				case 1: rs485_printf("45 SPS\r\n"); break;
				case 2: rs485_printf("90 SPS\r\n"); break;
				case 3: rs485_printf("175 SPS\r\n"); break;
				case 4: rs485_printf("330 SPS\r\n"); break;
				case 5: rs485_printf("600 SPS\r\n"); break;
				case 6: rs485_printf("1000 SPS\r\n"); break;
				default: rs485_printf("未知\r\n"); break;
			}
			rs485_printf("  工作模式: %s\r\n", op_mode == 0 ? "正常" : "其他");
			rs485_printf("  转换模式: %s\r\n", conv_mode == 0 ? "单次" : "连续");

			// 解析寄存器2 (CONFIG2)
			uint8_t vref = (reg_data[2] & 0xC0) >> 6;
			rs485_printf("寄存器2解析:\r\n");
			rs485_printf("  电压基准: ");
			switch(vref) {
				case 0: rs485_printf("内部2.048V\r\n"); break;
				case 1: rs485_printf("外部REFP/REFN\r\n"); break;
				case 2: rs485_printf("外部AVDD/AVSS\r\n"); break;
				case 3: rs485_printf("供电电压\r\n"); break;
			}

			// 目标配置验证
			rs485_printf("\r\n=== 目标配置对比 ===\r\n");
			rs485_printf("期望配置: 0x60 0x00 0x80 0x00\r\n");
			rs485_printf("实际配置: 0x%02X 0x%02X 0x%02X 0x%02X\r\n",
						reg_data[0], reg_data[1], reg_data[2], reg_data[3]);

			bool config_ok = true;
			if (reg_data[0] != 0x60) {
				rs485_printf("✗ 寄存器0不匹配 (期望:0x60, 实际:0x%02X)\r\n", reg_data[0]);
				config_ok = false;
			}
			if (reg_data[1] != 0x00) {
				rs485_printf("✗ 寄存器1不匹配 (期望:0x00, 实际:0x%02X)\r\n", reg_data[1]);
				config_ok = false;
			}
			if (reg_data[2] != 0x80) {
				rs485_printf("寄存器2不匹配 (期望:0x80, 实际:0x%02X)\r\n", reg_data[2]);
				config_ok = false;
			}
			if (reg_data[3] != 0x00) {
				rs485_printf("寄存器3不匹配 (期望:0x00, 实际:0x%02X)\r\n", reg_data[3]);
				config_ok = false;
			}

			if (config_ok) {
				rs485_printf("所有配置与目标一致！\r\n");
			} else {
				rs485_printf("配置存在差异，需要重新初始化\r\n");
			}

		} else {
			rs485_printf("读取寄存器失败，请检查SPI连接\r\n");
		}

		rs485_printf("=== 验证完成 ===\r\n");
	}
	else if(strncmp((char *)buffer, "command:ads1220_read_test", 25) == 0)
	{
		ADC_READ_TEST();
	}
}

void Uart2_Proc(void)
{
	uint16_t length;
	// 获取 "待处理货架" 中的数据长度
	length = rt_ringbuffer_data_len(&uart2_ringbuffer);
	// 如果 "待处理货架" 中没有数据，则直接返回
	if (length == 0)
		return;

	// 从 "待处理货架" 中获取数据，存入 uart_dma_buffer 中
	rt_ringbuffer_get(&uart2_ringbuffer, uart2_dma_buffer, length);

	// 3. 处理 "待处理货架" (uart_dma_buffer) 中的数据
	//    这里简单地打印出来，实际应用中会进行解析、执行命令等
	// my_printf(&huart1, "data: %s\n", uart_dma_buffer);
	rs485_printf("data: %s\r\n", uart2_dma_buffer);
	//    (注意：如果数据不是字符串，需要用其他方式处理，比如按字节解析)
	// 调用解析函数处理c串口1接收到的命令
	parse_uart_command(uart2_dma_buffer, length);
	prase_rs485_command(uart2_dma_buffer, length);
	// 4. 清空"待处理货架"，为下次接收做准备
	memset(uart2_dma_buffer, 0, sizeof(uart2_dma_buffer));
}

void system_selftest(void)
{
	char *TF_Test;
	char *selftest;

	// 初始化SPI Flash
	spi_flash_init();

	// 读取Flash ID和验证通信
	uint32_t flash_jedec_id = spi_flash_read_id();				  // JEDEC ID
	uint8_t flash_comm_status = spi_flash_verify_communication(); // 验证通信

	// 根据Flash通信状态设置自检结果
	if (flash_comm_status == 0)
	{
		selftest = "ok";
	}
	else
	{
		selftest = "error";
	}

	// TF卡内存变量，支持动态更新
	static char TF_card_memory[32] = "10000"; // 默认值
	uint32_t actual_memory_kb = 0;

	uint8_t sd_test = SD_Test(); // 测试TF卡

	// 如果TF卡存在，读取实际内存大小
	if (sd_test)
	{
		actual_memory_kb = SD_Get_Card_Memory_KB();
		if (actual_memory_kb > 0)
		{
			// 更新TF_card_memory的值
			snprintf(TF_card_memory, sizeof(TF_card_memory), "%lu", actual_memory_kb);
		}
		// 如果读取失败，保持默认值"10000"
	}

	rs485_printf("======system selftest======\r\n");
	rs485_printf("flash............%s\r\n", selftest);
	if (sd_test)
	{
		TF_Test = "OK";
		rs485_printf("TF card............%s\r\n", TF_Test);
	}
	else
	{
		TF_Test = "error";
		rs485_printf("TF card............%s\r\n", TF_Test);
		// 记录TF卡错误日志
		Write_Log_With_Timestamp_Universal("test error: tf card not found");
	}
	// 显示Flash详细信息
	rs485_printf("flash ID: 0x%06lX\r\n", flash_jedec_id);

	if (TF_Test == "error")
	{
		rs485_printf("can not find TF card\r\n");
	}
	else if (TF_Test == "OK")
	{
		rs485_printf("TF card memory: %s KB\r\n", TF_card_memory);
	}
	rs485_printf("RTC: %04d-%02d-%02d %02d:%02d:%02d\r\n",
				 2000 + Date.Year, Date.Month, Date.Date, Time.Hours, Time.Minutes, Time.Seconds);
	rs485_printf("======system selftest======\r\n");
}
