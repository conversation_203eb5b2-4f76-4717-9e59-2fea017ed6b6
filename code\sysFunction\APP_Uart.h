#ifndef __APP_UART__H
#define __APP_UART__H

#include "Mydefine.h"

#define IS_DIGIT(c) ((c) >= '0' && (c) <= '9')
#define UART_TIMEOUT_MS 100
// 配置保存地址定义
#define CONFIG_ADDRESS 0x0800C000 // 确保在Flash有效范围内

// 在文件开头添加RS485相关定义
// 当前定义使用PA1
#define RS485_DE_RE_PIN    GPIO_PIN_1
#define RS485_DE_RE_PORT   GPIOA
#define RS485_TX_MODE()    HAL_GPIO_WritePin(RS485_DE_RE_PORT, RS485_DE_RE_PIN, GPIO_PIN_SET)    //高电平
#define RS485_RX_MODE()    HAL_GPIO_WritePin(RS485_DE_RE_PORT, RS485_DE_RE_PIN, GPIO_PIN_RESET)  //低电平 

void system_selftest(void);

// 新增RS485输出函数声明
int rs485_printf(const char *format, ...);                    // RS485格式化输出
int rs485_printf_dma(const char *format, ...);                // RS485 DMA格式化输出

extern uint16_t uart2_rx_index;
extern uint32_t uart2_rx_ticks;
extern uint8_t uart2_rx_buffer[128];
extern uint8_t uart2_rx_dma_buffer[128];
extern uint8_t uart2_dma_buffer[128];
extern uint8_t uart2_flag;
extern struct rt_ringbuffer uart2_ringbuffer;  // 环形缓冲区

extern uint8_t uart2_ringbuffer_pool[128];
extern uint8_t Led_ADC_Mode;
extern uint8_t hide_config_step; // hide配置模式
extern double Ratio;//变比
extern double limit;//阈值
extern uint32_t sample_cycle; // 采样周期

// RS485相关函数声明
HAL_StatusTypeDef RS485_Transmit(uint8_t *data, uint16_t size, uint32_t timeout);
HAL_StatusTypeDef RS485_Transmit_DMA(uint8_t *data, uint16_t size);
void Uart2_Proc(void);

// RTC时间解析函数声明
HAL_StatusTypeDef parse_rtc_datetime_string(const char *datetime_str, RTC_TimeTypeDef *time, RTC_DateTypeDef *date);


#endif




