#ifndef __MYDEFINE__H
#define __MYDEFINE__H

#include "main.h"
#include "dma.h"
#include "usart.h"
#include "ringbuffer.h"
#include "adc.h"
#include "tim.h"
#include "math.h"
#include "i2c.h"
#include "oled.h"
#include "rtc.h"
#include "gd25qxx.h"
#include "spi.h"

#include "Schedular.h"
#include "APP_Led.h"
#include "APP_Key.h"
#include "APP_Uart.h"
#include "APP_ADC.h"
#include "APP_OLED.h"
#include "APP_RTC.h"
#include "APP_FLASH.h"
#include "APP_EXTSPI.h"
#include "ADS1220.h"

#include "stdio.h"
#include "string.h"
#include "stdarg.h"



#endif
