#include "Schedular.h"

/*
*该函数为调度器
*开源来自于Alice_西风
*可以用于替代Rtos等操作系统
*主要分为以下两个模块
*初始化：计算任务数量
*运行函数：用于遍历各个任务使得任务得以按时间进行运行
*/

uint32_t sample_cycle = 5000; //采样周期
// 全局变量用于存储任务数量
uint8_t task_num;

// 结构体 用于表示任务的一些数据
typedef struct
{
    void (*task_func)(void); // 函数任务名称
    uint32_t rate_ms;        // 任务运行周期
    uint32_t last_run;       // 上一次运行时间
} task_t;

// 静态任务数组，每个任务都包含有任务函数，运行周期（ms）和上一次运行时间（ms）
static task_t schedular_task[] = {
    {Led_Proc, 1, 0},
    {Key_Proc, 10, 0},
	{ADC_Proc, 100, 0},
    {oled_task, 10, 0},
    {RTC_Task, 1000, 0},
    {Uart2_Proc, 10, 0},
    {ADC_TASK, 5000, 0},
    {LED_SHINE, 500, 0},
    {ADC_Data_Storage_Task, 5000, 0}  // 数据存储任务，与ADC_TASK同步
};


/*
* 更新ADC 和 Oled采样周期
* 参数：new_cycle - 新的采样周期（10, 5000, 10000或15000）
*/
void Update_Sample_Cycle(uint32_t new_cycle)
{
    if(new_cycle == 5000 || new_cycle == 10000 || new_cycle == 15000) {
        sample_cycle = new_cycle;
        schedular_task[ADC_TASK_INDEX].rate_ms = sample_cycle;
        schedular_task[OLED_TASK_INDEX].rate_ms = sample_cycle;
        schedular_task[ADC_DATA_STORAGE_TASK_INDEX].rate_ms = sample_cycle;  // 同步数据存储任务
    }
    if(new_cycle == 10)
    {
        sample_cycle = new_cycle;
        schedular_task[OLED_TASK_INDEX].rate_ms = sample_cycle;
        // 数据存储任务保持原有周期，不跟随快速采样
    }
}

/*
*任务初始化调度函数
*用于计算任务数组的元素个数，并将其结果存储在 task_num 中
*/
void Schedular_Init()
{
    //计算任务数量
    task_num = sizeof(schedular_task) / sizeof(task_t);
}

/*
*调度器运行函数
*遍历任务数组，检查是否有任务需要执行，如果当前时间已经超出了任务的执行周期，则执行该任务且将该任务时间更新为上次执行时间
*/

void Schedular_Run()
{
    for(uint8_t i = 0; i < task_num; i++)\
    {
        uint32_t now_time = HAL_GetTick();
        if(now_time >= schedular_task[i].rate_ms + schedular_task[i].last_run)
        {
            schedular_task[i].task_func();
            schedular_task[i].last_run = now_time;
        }
    }
}


