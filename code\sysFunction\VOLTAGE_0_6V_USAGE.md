# ADS1220 0-6V电压测量使用说明

## 📋 概述

本模块专门用于测量0-6V的外部电压。通过硬件将0-6V电压降压到0-3V后输入到ADS1220进行高精度测量，然后通过软件将测量结果还原为实际的0-6V电压值。

## ⚙️ 硬件配置

### 电压处理链路
```
外部电压(0-6V) → 硬件降压电路(2:1) → ADS1220输入(0-3V) → 软件还原(0-6V)
```

### 关键参数
- **外部电压范围**: 0-6V
- **ADC输入范围**: 0-3V  
- **降压比例**: 2:1 (6V → 3V)
- **测量精度**: 24位ADC (~0.36μV @ 3V基准)
- **基准电压**: 3.3V (AVDD/AVSS)

## 🚀 函数使用方法

### 1. 基本电压读取
```c
float external_voltage;

// 读取AIN0通道的0-6V电压
if (ADS1220_ReadExternalVoltage_0_6V(ADS1220_CHANNEL_AIN0, &external_voltage) == HAL_OK) {
    printf("外部电压: %.3fV\n", external_voltage);
} else {
    printf("读取失败\n");
}
```

### 2. 多通道同时读取
```c
float voltages[3];  // AIN0, AIN1, AIN2

// 读取所有三个通道
if (ADS1220_ReadMultiChannel_0_6V(voltages) == HAL_OK) {
    printf("AIN0: %.3fV\n", voltages[0]);
    printf("AIN1: %.3fV\n", voltages[1]);
    printf("AIN2: %.3fV\n", voltages[2]);
}
```

### 3. 获取详细测量信息
```c
float adc_voltage, external_voltage;
int32_t raw_data;

// 获取完整的测量信息
if (ADS1220_GetVoltageDetails_0_6V(ADS1220_CHANNEL_AIN0, 
                                   &adc_voltage, &external_voltage, &raw_data) == HAL_OK) {
    printf("ADC电压: %.6fV (0-3V)\n", adc_voltage);
    printf("外部电压: %.6fV (0-6V)\n", external_voltage);
    printf("原始数据: %ld\n", raw_data);
}
```

### 4. 电压监控
```c
// 监控AIN0通道，设置阈值为1.0V-5.0V
uint8_t status = ADS1220_VoltageMonitor_0_6V(ADS1220_CHANNEL_AIN0, 5.0f, 1.0f);

switch(status) {
    case VOLTAGE_STATUS_NORMAL:
        printf("电压正常\n");
        break;
    case VOLTAGE_STATUS_HIGH:
        printf("过压警告\n");
        break;
    case VOLTAGE_STATUS_LOW:
        printf("欠压警告\n");
        break;
    case VOLTAGE_STATUS_ERROR:
        printf("读取错误\n");
        break;
}
```

## 📡 RS485测试命令

### 1. 基本电压测量
```bash
command:voltage_0_6v
```
**功能**: 读取AIN0、AIN1、AIN2三个通道的0-6V电压

**输出示例**:
```
=== 0-6V外部电压测量 ===
AIN0外部电压: 2.456V
AIN1外部电压: 1.234V
AIN2外部电压: 4.567V
=== 测量完成 ===
```

### 2. 详细测量信息
```bash
command:voltage_detail
```
**功能**: 显示每个通道的详细测量信息

**输出示例**:
```
=== 详细电压测量信息 ===
AIN0详细信息:
  ADC电压: 1.228000V (0-3V范围)
  外部电压: 2.456000V (0-6V范围)
  原始数据: 3145728
  缩放比例: 2.0:1
AIN1详细信息:
  ADC电压: 0.617000V (0-3V范围)
  外部电压: 1.234000V (0-6V范围)
  原始数据: 1572864
  缩放比例: 2.0:1
=== 详细测量完成 ===
```

### 3. 电压监控测试
```bash
command:voltage_monitor
```
**功能**: 监控AIN0通道电压，检查是否在1.0V-5.0V范围内

**输出示例**:
```
=== 电压监控测试 (AIN0) ===
监控范围: 1.0V - 5.0V
✓ 电压正常
当前AIN0电压: 2.456V
=== 监控完成 ===
```

## 🔧 核心函数详解

### `ADS1220_ReadExternalVoltage_0_6V()`
- **功能**: 读取单个通道的0-6V外部电压
- **转换公式**: `外部电压 = ADC电压 × 2.0`
- **范围限制**: 自动限制在0-6V范围内
- **错误处理**: 完整的参数检查和错误返回

### `ADS1220_ReadMultiChannel_0_6V()`
- **功能**: 一次读取多个通道
- **通道间延时**: 10ms，确保稳定切换
- **错误标记**: 失败的通道标记为-1.0V
- **返回状态**: 任一通道失败则返回HAL_ERROR

### `ADS1220_GetVoltageDetails_0_6V()`
- **功能**: 获取完整的测量信息
- **包含数据**: ADC电压、外部电压、原始数据
- **精度**: 显示完整的6位小数精度
- **调试用途**: 适合故障排除和精度验证

### `ADS1220_VoltageMonitor_0_6V()`
- **功能**: 实时电压监控
- **阈值检查**: 可配置的高低阈值
- **状态返回**: 4种状态（正常/过压/欠压/错误）
- **应用场景**: 电源监控、保护电路

## ⚠️ 注意事项

### 1. 硬件要求
- 确保降压电路的比例准确为2:1
- 输入电压不要超过6V
- 降压后的电压不要超过3V

### 2. 软件配置
- ADS1220必须已正确初始化
- 基准电压设置为AVDD/AVSS (3.3V)
- 确保单端输入模式配置正确

### 3. 测量精度
- 理论精度: 6V / 2^23 ≈ 0.715μV
- 实际精度受硬件降压电路精度影响
- 建议定期校准降压比例

### 4. 性能考虑
- 每次测量约需50ms (20SPS模式)
- 多通道测量有额外的通道切换延时
- 连续测量时注意功耗管理

## 🔍 故障排除

### 常见问题

1. **读取值始终为0**
   - 检查ADS1220初始化状态
   - 确认输入信号连接
   - 验证降压电路工作

2. **读取值不准确**
   - 校准降压电路比例
   - 检查基准电压稳定性
   - 验证ADC配置

3. **读取失败**
   - 检查SPI通信
   - 确认DRDY信号
   - 验证超时设置

### 调试建议
- 使用`command:voltage_detail`查看详细信息
- 对比ADC电压和外部电压的比例关系
- 使用万用表验证实际电压值

## 📊 性能指标

- **测量范围**: 0-6V
- **分辨率**: 24位 (~0.715μV理论值)
- **采样率**: 20 SPS
- **转换时间**: ~50ms
- **精度**: 取决于硬件降压电路
- **稳定性**: ±0.01% (典型值)
